-- Finance Records Table
CREATE TABLE IF NOT EXISTS finance_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    dashsy_revenue DECIMAL(10,2) DEFAULT 0,
    syriana_revenue DECIMAL(10,2) DEFAULT 0,
    booking_revenue DECIMAL(10,2) DEFAULT 0,
    templates_revenue DECIMAL(10,2) DEFAULT 0,
    apps_revenue DECIMAL(10,2) DEFAULT 0,
    other_revenue DECIMAL(10,2) DEFAULT 0,
    total_revenue DECIMAL(10,2) DEFAULT 0,
    total_expenses DECIMAL(10,2) DEFAULT 0,
    net_profit DECIMAL(10,2) DEFAULT 0,
    finance_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- Template Records Table
CREATE TABLE IF NOT EXISTS template_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    shopify_time INTEGER DEFAULT 0, -- minutes
    salla_time INTEGER DEFAULT 0, -- minutes
    wordpress_time INTEGER DEFAULT 0, -- minutes
    shopify_progress INTEGER DEFAULT 0, -- percentage
    salla_progress INTEGER DEFAULT 0, -- percentage
    wordpress_progress INTEGER DEFAULT 0, -- percentage
    shopify_templates TEXT,
    salla_templates TEXT,
    wordpress_templates TEXT,
    total_hours DECIMAL(5,2) DEFAULT 0,
    template_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- App Records Table
CREATE TABLE IF NOT EXISTS app_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    date DATE NOT NULL,
    shopify_app_time INTEGER DEFAULT 0, -- minutes
    salla_app_time INTEGER DEFAULT 0, -- minutes
    wordpress_plugin_time INTEGER DEFAULT 0, -- minutes
    mobile_app_time INTEGER DEFAULT 0, -- minutes
    shopify_app_progress INTEGER DEFAULT 0, -- percentage
    salla_app_progress INTEGER DEFAULT 0, -- percentage
    wordpress_plugin_progress INTEGER DEFAULT 0, -- percentage
    mobile_app_progress INTEGER DEFAULT 0, -- percentage
    shopify_apps TEXT,
    salla_apps TEXT,
    wordpress_plugins TEXT,
    mobile_apps TEXT,
    total_hours DECIMAL(5,2) DEFAULT 0,
    app_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- Weekly Records Table
CREATE TABLE IF NOT EXISTS weekly_records (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    week_start DATE NOT NULL,
    week_end DATE NOT NULL,
    worship_average INTEGER DEFAULT 0, -- percentage
    learning_hours DECIMAL(5,2) DEFAULT 0,
    project_hours DECIMAL(5,2) DEFAULT 0,
    template_hours DECIMAL(5,2) DEFAULT 0,
    app_hours DECIMAL(5,2) DEFAULT 0,
    total_revenue DECIMAL(10,2) DEFAULT 0,
    net_profit DECIMAL(10,2) DEFAULT 0,
    goals_achieved TEXT,
    challenges_faced TEXT,
    lessons_learned TEXT,
    next_week_goals TEXT,
    overall_rating INTEGER DEFAULT 5 CHECK (overall_rating >= 1 AND overall_rating <= 10),
    weekly_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, week_start)
);

-- Enable Row Level Security
ALTER TABLE finance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE template_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE app_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE weekly_records ENABLE ROW LEVEL SECURITY;

-- Create RLS Policies for Finance Records
CREATE POLICY "Users can view their own finance records" ON finance_records
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own finance records" ON finance_records
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own finance records" ON finance_records
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own finance records" ON finance_records
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS Policies for Template Records
CREATE POLICY "Users can view their own template records" ON template_records
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own template records" ON template_records
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own template records" ON template_records
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own template records" ON template_records
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS Policies for App Records
CREATE POLICY "Users can view their own app records" ON app_records
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own app records" ON app_records
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own app records" ON app_records
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own app records" ON app_records
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS Policies for Weekly Records
CREATE POLICY "Users can view their own weekly records" ON weekly_records
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own weekly records" ON weekly_records
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own weekly records" ON weekly_records
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own weekly records" ON weekly_records
    FOR DELETE USING (auth.uid() = user_id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_finance_records_user_date ON finance_records(user_id, date);
CREATE INDEX IF NOT EXISTS idx_template_records_user_date ON template_records(user_id, date);
CREATE INDEX IF NOT EXISTS idx_app_records_user_date ON app_records(user_id, date);
CREATE INDEX IF NOT EXISTS idx_weekly_records_user_week ON weekly_records(user_id, week_start);

-- Create functions to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_finance_records_updated_at BEFORE UPDATE ON finance_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_template_records_updated_at BEFORE UPDATE ON template_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_app_records_updated_at BEFORE UPDATE ON app_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_weekly_records_updated_at BEFORE UPDATE ON weekly_records
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
