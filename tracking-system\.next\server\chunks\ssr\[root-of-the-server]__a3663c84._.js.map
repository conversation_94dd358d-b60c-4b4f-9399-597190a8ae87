{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { getSupabase } from '@/lib/supabase'\nimport { initializeApp, getUserProfile } from '@/lib/database-setup'\n\ninterface DashboardStats {\n  routineStatus: 'green' | 'yellow' | 'red'\n  learningProgress: number\n  templatesProgress: number\n  projectsProgress: number\n  dailyRevenue: number\n  monthlyRoutinePercentage: number\n  monthlyLearningHours: number\n  monthlyTemplatesProgress: number\n  monthlyProjectsProgress: number\n}\n\nexport default function DashboardPage() {\n  const [stats, setStats] = useState<DashboardStats>({\n    routineStatus: 'green',\n    learningProgress: 75,\n    templatesProgress: 60,\n    projectsProgress: 85,\n    dailyRevenue: 150,\n    monthlyRoutinePercentage: 80,\n    monthlyLearningHours: 120,\n    monthlyTemplatesProgress: 65,\n    monthlyProjectsProgress: 78,\n  })\n  const [loading, setLoading] = useState(false)\n\n  useEffect(() => {\n    // Simulate loading demo data\n    setLoading(false)\n  }, [])\n\n  const handleSignOut = async () => {\n    // For demo purposes, just redirect to login\n    window.location.href = '/login'\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'green': return 'bg-green-100 text-green-800'\n      case 'yellow': return 'bg-yellow-100 text-yellow-800'\n      case 'red': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusEmoji = (status: string) => {\n    switch (status) {\n      case 'green': return '🟢'\n      case 'yellow': return '🟡'\n      case 'red': return '🔴'\n      default: return '⚪'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                🕌 نظام المتابعة الشامل\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <span className=\"text-sm text-gray-600\">\n                مرحباً، مستخدم تجريبي\n              </span>\n              <button\n                onClick={handleSignOut}\n                className=\"text-sm text-red-600 hover:text-red-800\"\n              >\n                تسجيل الخروج\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">{getStatusEmoji(stats.routineStatus)}</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">حالة الروتين</p>\n                <p className={`text-sm px-2 py-1 rounded-full ${getStatusColor(stats.routineStatus)}`}>\n                  {stats.routineStatus === 'green' ? 'ممتاز' : \n                   stats.routineStatus === 'yellow' ? 'جيد' : 'يحتاج تحسين'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">📚</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">تقدم التعلم</p>\n                <p className=\"text-2xl font-bold text-blue-600\">{stats.learningProgress}%</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">🛠️</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">تقدم المشاريع</p>\n                <p className=\"text-2xl font-bold text-green-600\">{stats.projectsProgress}%</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">💰</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">الإيرادات اليومية</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">${stats.dailyRevenue}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Link href=\"/worship\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">🕌</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">العبادات والروتين</h3>\n              <p className=\"text-sm text-gray-600\">متابعة الصلوات والأذكار والروتين اليومي</p>\n            </div>\n          </Link>\n\n          <Link href=\"/learning\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">📚</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">التعلم</h3>\n              <p className=\"text-sm text-gray-600\">تتبع تعلم Laravel, MEAN Stack, Flutter</p>\n            </div>\n          </Link>\n\n          <Link href=\"/templates\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">🎨</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">القوالب والمنصات</h3>\n              <p className=\"text-sm text-gray-600\">تطوير قوالب Shopify, Salla, WordPress</p>\n            </div>\n          </Link>\n\n          <Link href=\"/apps\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">📱</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">تطبيقات المنصات</h3>\n              <p className=\"text-sm text-gray-600\">تطوير التطبيقات المرتبطة بالمنصات</p>\n            </div>\n          </Link>\n\n          <Link href=\"/projects\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">🏢</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">مشاريع الشركة</h3>\n              <p className=\"text-sm text-gray-600\">Dash-sy, Syriana Store, Booking-Sy</p>\n            </div>\n          </Link>\n\n          <Link href=\"/finance\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">💰</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">الإيرادات والمالية</h3>\n              <p className=\"text-sm text-gray-600\">تتبع الإيرادات والمصروفات</p>\n            </div>\n          </Link>\n\n          <Link href=\"/weekly\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">📊</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">المتابعة الأسبوعية</h3>\n              <p className=\"text-sm text-gray-600\">تقييم أسبوعي شامل للأداء</p>\n            </div>\n          </Link>\n\n          <Link href=\"/analytics\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">📈</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">التحليلات</h3>\n              <p className=\"text-sm text-gray-600\">رسوم بيانية وإحصائيات مفصلة</p>\n            </div>\n          </Link>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AAJA;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,eAAe;QACf,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,cAAc;QACd,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,yBAAyB;IAC3B;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,6BAA6B;QAC7B,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,4CAA4C;QAC5C,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;kDAGxC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAY,eAAe,MAAM,aAAa;;;;;;;;;;;sDAEhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAW,CAAC,+BAA+B,EAAE,eAAe,MAAM,aAAa,GAAG;8DAClF,MAAM,aAAa,KAAK,UAAU,UAClC,MAAM,aAAa,KAAK,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAMpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAoC,MAAM,gBAAgB;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK9E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAqC,MAAM,gBAAgB;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK/E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAqC;wDAAE,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAC9B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAC9B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAC7B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}]}