'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { getSupabase } from '@/lib/supabase'
import { initializeApp, getUserProfile } from '@/lib/database-setup'

interface DashboardStats {
  routineStatus: 'green' | 'yellow' | 'red'
  learningProgress: number
  templatesProgress: number
  projectsProgress: number
  dailyRevenue: number
  monthlyRoutinePercentage: number
  monthlyLearningHours: number
  monthlyTemplatesProgress: number
  monthlyProjectsProgress: number
}

export default function DashboardPage() {
  const [stats, setStats] = useState<DashboardStats>({
    routineStatus: 'green',
    learningProgress: 75,
    templatesProgress: 60,
    projectsProgress: 85,
    dailyRevenue: 150,
    monthlyRoutinePercentage: 80,
    monthlyLearningHours: 120,
    monthlyTemplatesProgress: 65,
    monthlyProjectsProgress: 78,
  })
  const [loading, setLoading] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [userProfile, setUserProfile] = useState<any>(null)
  const router = useRouter()

  useEffect(() => {
    checkAuth()
    initializeDatabase()
  }, [])

  const checkAuth = async () => {
    try {
      const supabase = await getSupabase()
      const { data: { user }, error } = await supabase.auth.getUser()

      if (error || !user) {
        router.push('/login')
        return
      }

      setUser(user)

      // Get user profile
      const profileResult = await getUserProfile(user.id)
      if (profileResult.success) {
        setUserProfile(profileResult.data)
      }

    } catch (error) {
      console.error('Auth check error:', error)
      router.push('/login')
    } finally {
      setLoading(false)
    }
  }

  const initializeDatabase = async () => {
    const result = await initializeApp()
    console.log('Database initialization:', result)
  }

  const handleSignOut = async () => {
    try {
      const supabase = await getSupabase()
      await supabase.auth.signOut()
      router.push('/login')
    } catch (error) {
      console.error('Sign out error:', error)
    }
  }



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'green': return 'bg-green-100 text-green-800'
      case 'yellow': return 'bg-yellow-100 text-yellow-800'
      case 'red': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusEmoji = (status: string) => {
    switch (status) {
      case 'green': return '🟢'
      case 'yellow': return '🟡'
      case 'red': return '🔴'
      default: return '⚪'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                🕌 نظام المتابعة الشامل
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <span className="text-sm text-gray-600">
                مرحباً، {userProfile?.full_name || user?.email || 'مستخدم'}
              </span>
              <button
                onClick={handleSignOut}
                className="text-sm text-red-600 hover:text-red-800"
              >
                تسجيل الخروج
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">{getStatusEmoji(stats.routineStatus)}</span>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">حالة الروتين</p>
                <p className={`text-sm px-2 py-1 rounded-full ${getStatusColor(stats.routineStatus)}`}>
                  {stats.routineStatus === 'green' ? 'ممتاز' : 
                   stats.routineStatus === 'yellow' ? 'جيد' : 'يحتاج تحسين'}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📚</span>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">تقدم التعلم</p>
                <p className="text-2xl font-bold text-blue-600">{stats.learningProgress}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🛠️</span>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">تقدم المشاريع</p>
                <p className="text-2xl font-bold text-green-600">{stats.projectsProgress}%</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">💰</span>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">الإيرادات اليومية</p>
                <p className="text-2xl font-bold text-yellow-600">${stats.dailyRevenue}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Link href="/worship" className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block">
            <div className="text-center">
              <span className="text-4xl mb-4 block">🕌</span>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">العبادات والروتين</h3>
              <p className="text-sm text-gray-600">متابعة الصلوات والأذكار والروتين اليومي</p>
            </div>
          </Link>

          <Link href="/learning" className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block">
            <div className="text-center">
              <span className="text-4xl mb-4 block">📚</span>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">التعلم</h3>
              <p className="text-sm text-gray-600">تتبع تعلم Laravel, MEAN Stack, Flutter</p>
            </div>
          </Link>

          <Link href="/templates" className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block">
            <div className="text-center">
              <span className="text-4xl mb-4 block">🎨</span>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">القوالب والمنصات</h3>
              <p className="text-sm text-gray-600">تطوير قوالب Shopify, Salla, WordPress</p>
            </div>
          </Link>

          <Link href="/apps" className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block">
            <div className="text-center">
              <span className="text-4xl mb-4 block">📱</span>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">تطبيقات المنصات</h3>
              <p className="text-sm text-gray-600">تطوير التطبيقات المرتبطة بالمنصات</p>
            </div>
          </Link>

          <Link href="/projects" className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block">
            <div className="text-center">
              <span className="text-4xl mb-4 block">🏢</span>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">مشاريع الشركة</h3>
              <p className="text-sm text-gray-600">Dash-sy, Syriana Store, Booking-Sy</p>
            </div>
          </Link>

          <Link href="/finance" className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block">
            <div className="text-center">
              <span className="text-4xl mb-4 block">💰</span>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">الإيرادات والمالية</h3>
              <p className="text-sm text-gray-600">تتبع الإيرادات والمصروفات</p>
            </div>
          </Link>

          <Link href="/weekly" className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block">
            <div className="text-center">
              <span className="text-4xl mb-4 block">📊</span>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">المتابعة الأسبوعية</h3>
              <p className="text-sm text-gray-600">تقييم أسبوعي شامل للأداء</p>
            </div>
          </Link>

          <Link href="/analytics" className="bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block">
            <div className="text-center">
              <span className="text-4xl mb-4 block">📈</span>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">التحليلات</h3>
              <p className="text-sm text-gray-600">رسوم بيانية وإحصائيات مفصلة</p>
            </div>
          </Link>
        </div>
      </main>
    </div>
  )
}
