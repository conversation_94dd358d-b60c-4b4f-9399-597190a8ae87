{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/components/ui/toast.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useCallback } from 'react'\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'\n\ntype ToastType = 'success' | 'error' | 'info' | 'warning'\n\ninterface Toast {\n  id: string\n  type: ToastType\n  title: string\n  message?: string\n  duration?: number\n}\n\ninterface ToastContextType {\n  toasts: Toast[]\n  addToast: (toast: Omit<Toast, 'id'>) => void\n  removeToast: (id: string) => void\n}\n\nconst ToastContext = createContext<ToastContextType | undefined>(undefined)\n\nexport function ToastProvider({ children }: { children: React.ReactNode }) {\n  const [toasts, setToasts] = useState<Toast[]>([])\n\n  const addToast = useCallback((toast: Omit<Toast, 'id'>) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    const newToast = { ...toast, id }\n    \n    setToasts(prev => [...prev, newToast])\n    \n    // Auto remove after duration (default 5 seconds)\n    setTimeout(() => {\n      setToasts(prev => prev.filter(t => t.id !== id))\n    }, toast.duration || 5000)\n  }, [])\n\n  const removeToast = useCallback((id: string) => {\n    setToasts(prev => prev.filter(t => t.id !== id))\n  }, [])\n\n  return (\n    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>\n      {children}\n      <ToastContainer />\n    </ToastContext.Provider>\n  )\n}\n\nexport function useToast() {\n  const context = useContext(ToastContext)\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider')\n  }\n  return context\n}\n\nfunction ToastContainer() {\n  const { toasts, removeToast } = useToast()\n\n  if (toasts.length === 0) return null\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2\">\n      {toasts.map(toast => (\n        <ToastItem key={toast.id} toast={toast} onRemove={removeToast} />\n      ))}\n    </div>\n  )\n}\n\nfunction ToastItem({ toast, onRemove }: { toast: Toast; onRemove: (id: string) => void }) {\n  const getIcon = () => {\n    switch (toast.type) {\n      case 'success':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />\n      case 'error':\n        return <AlertCircle className=\"h-5 w-5 text-red-500\" />\n      case 'warning':\n        return <AlertTriangle className=\"h-5 w-5 text-yellow-500\" />\n      case 'info':\n        return <Info className=\"h-5 w-5 text-blue-500\" />\n    }\n  }\n\n  const getBackgroundColor = () => {\n    switch (toast.type) {\n      case 'success':\n        return 'bg-green-50 border-green-200'\n      case 'error':\n        return 'bg-red-50 border-red-200'\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200'\n      case 'info':\n        return 'bg-blue-50 border-blue-200'\n    }\n  }\n\n  return (\n    <div className={`max-w-sm w-full border rounded-lg shadow-lg p-4 ${getBackgroundColor()} animate-in slide-in-from-right duration-300`}>\n      <div className=\"flex items-start\">\n        <div className=\"flex-shrink-0\">\n          {getIcon()}\n        </div>\n        <div className=\"mr-3 flex-1\">\n          <p className=\"text-sm font-medium text-gray-900\">{toast.title}</p>\n          {toast.message && (\n            <p className=\"mt-1 text-sm text-gray-600\">{toast.message}</p>\n          )}\n        </div>\n        <div className=\"flex-shrink-0 mr-2\">\n          <button\n            onClick={() => onRemove(toast.id)}\n            className=\"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600\"\n          >\n            <X className=\"h-4 w-4\" />\n          </button>\n        </div>\n      </div>\n    </div>\n  )\n}\n\n// Helper functions for easy use\nexport const toast = {\n  success: (title: string, message?: string, duration?: number) => {\n    // This will be used with the hook\n    return { type: 'success' as const, title, message, duration }\n  },\n  error: (title: string, message?: string, duration?: number) => {\n    return { type: 'error' as const, title, message, duration }\n  },\n  info: (title: string, message?: string, duration?: number) => {\n    return { type: 'info' as const, title, message, duration }\n  },\n  warning: (title: string, message?: string, duration?: number) => {\n    return { type: 'warning' as const, title, message, duration }\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;;;;;;AAFA;;;;AAqBA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAEhD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC5B,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,MAAM,WAAW;YAAE,GAAG,KAAK;YAAE;QAAG;QAEhC,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;QAErC,iDAAiD;QACjD,WAAW;YACT,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC9C,GAAG,MAAM,QAAQ,IAAI;IACvB,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAC9C,GAAG,EAAE;IAEL,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAQ;YAAU;QAAY;;YAC3D;0BACD,8OAAC;;;;;;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEA,SAAS;IACP,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;IAEhC,IAAI,OAAO,MAAM,KAAK,GAAG,OAAO;IAEhC,qBACE,8OAAC;QAAI,WAAU;kBACZ,OAAO,GAAG,CAAC,CAAA,sBACV,8OAAC;gBAAyB,OAAO;gBAAO,UAAU;eAAlC,MAAM,EAAE;;;;;;;;;;AAIhC;AAEA,SAAS,UAAU,EAAE,KAAK,EAAE,QAAQ,EAAoD;IACtF,MAAM,UAAU;QACd,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,qBAAO,8OAAC;oBAAY,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC;oBAAY,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,8OAAC;oBAAc,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,8OAAC;oBAAK,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,qBAAqB;QACzB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,gDAAgD,EAAE,qBAAqB,4CAA4C,CAAC;kBACnI,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAqC,MAAM,KAAK;;;;;;wBAC5D,MAAM,OAAO,kBACZ,8OAAC;4BAAE,WAAU;sCAA8B,MAAM,OAAO;;;;;;;;;;;;8BAG5D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,SAAS,IAAM,SAAS,MAAM,EAAE;wBAChC,WAAU;kCAEV,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzB;AAGO,MAAM,QAAQ;IACnB,SAAS,CAAC,OAAe,SAAkB;QACzC,kCAAkC;QAClC,OAAO;YAAE,MAAM;YAAoB;YAAO;YAAS;QAAS;IAC9D;IACA,OAAO,CAAC,OAAe,SAAkB;QACvC,OAAO;YAAE,MAAM;YAAkB;YAAO;YAAS;QAAS;IAC5D;IACA,MAAM,CAAC,OAAe,SAAkB;QACtC,OAAO;YAAE,MAAM;YAAiB;YAAO;YAAS;QAAS;IAC3D;IACA,SAAS,CAAC,OAAe,SAAkB;QACzC,OAAO;YAAE,MAAM;YAAoB;YAAO;YAAS;QAAS;IAC9D;AACF", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 282, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}