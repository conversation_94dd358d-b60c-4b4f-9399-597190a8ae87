{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_42037a09._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_0a9ce68d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pv/b0sORBABMXmX3QMQWZsS+dcYxjJC5gWEWeVaDG9Y=", "__NEXT_PREVIEW_MODE_ID": "b3da89a2abe7f0d7fb9139bc1c48a53e", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "34fe1959aaef47d8d0d1abf173e703d4ce5355db511f656d1b4cea8ff4ef74c7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "fe2d44240b2d2ca0d3999a76c8580d73ad48ac80e186e2568e32c40346cb323e"}}}, "instrumentation": null, "functions": {}}