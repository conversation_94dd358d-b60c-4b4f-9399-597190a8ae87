{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "pv/b0sORBABMXmX3QMQWZsS+dcYxjJC5gWEWeVaDG9Y=", "__NEXT_PREVIEW_MODE_ID": "d90bb03b3375ed6a889f8a75d257c62d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bc457a4fdc2ab69d4dee0aff6bab102dc26f8dbdb22a8d06c4ee71fc856c9202", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "759bd2516acc963dbf00e69fceb3241ab1590557685563aaa6fd3cab5f20cdbf"}}}, "sortedMiddleware": ["/"], "functions": {}}