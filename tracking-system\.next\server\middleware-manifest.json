{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "eUmVHv9brfhDaod+E4T//CFOxyPFr4buWIa4cmdcCaI=", "__NEXT_PREVIEW_MODE_ID": "bfffe312976508786866d3a07dd0c3c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9e516faae06e645ae5edf794426ea0c5f692ee9d72092af2d900829b52cff957", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "f4d74b98d3ba2b08000ee7cf652a7009fc1d2389c76fed8f6af5598484c4198d"}}}, "sortedMiddleware": ["/"], "functions": {}}