{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/lib/supabase.ts"], "sourcesContent": ["// Temporary authentication system using localStorage\ninterface User {\n  id: string\n  email: string\n  full_name?: string\n  created_at: string\n}\n\ninterface AuthResponse {\n  data: { user: User | null }\n  error: any\n}\n\n// Mock database using localStorage\nconst getStoredUsers = (): User[] => {\n  if (typeof window === 'undefined') return []\n  const users = localStorage.getItem('tracking_users')\n  return users ? JSON.parse(users) : []\n}\n\nconst saveUsers = (users: User[]) => {\n  if (typeof window === 'undefined') return\n  localStorage.setItem('tracking_users', JSON.stringify(users))\n}\n\nconst getCurrentUser = (): User | null => {\n  if (typeof window === 'undefined') return null\n  const user = localStorage.getItem('tracking_current_user')\n  return user ? JSON.parse(user) : null\n}\n\nconst setCurrentUser = (user: User | null) => {\n  if (typeof window === 'undefined') return\n  if (user) {\n    localStorage.setItem('tracking_current_user', JSON.stringify(user))\n  } else {\n    localStorage.removeItem('tracking_current_user')\n  }\n}\n\n// Initialize demo users if none exist\nconst initializeDemoUsers = () => {\n  if (typeof window === 'undefined') return\n  const existingUsers = getStoredUsers()\n  if (existingUsers.length === 0) {\n    const demoUsers = [\n      {\n        id: 'demo-user-1',\n        email: '<EMAIL>',\n        full_name: 'مستخدم تجريبي',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'user-test-1',\n        email: '<EMAIL>',\n        full_name: 'سمير الكحلان',\n        created_at: new Date().toISOString()\n      }\n    ]\n    saveUsers(demoUsers)\n  }\n}\n\n// Mock Supabase client\nconst mockSupabase = {\n  auth: {\n    getUser: async (): Promise<AuthResponse> => {\n      const user = getCurrentUser()\n      return { data: { user }, error: null }\n    },\n\n    signOut: async () => {\n      setCurrentUser(null)\n      return { error: null }\n    },\n\n    signInWithPassword: async ({ email, password }: { email: string, password: string }) => {\n      const users = getStoredUsers()\n      const user = users.find(u => u.email === email)\n\n      if (!user) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني غير مسجل' }\n        }\n      }\n\n      // For demo, accept any password\n      setCurrentUser(user)\n      return { data: { user }, error: null }\n    },\n\n    signUp: async ({ email, password, options }: {\n      email: string,\n      password: string,\n      options?: { data?: { full_name?: string } }\n    }) => {\n      const users = getStoredUsers()\n      const existingUser = users.find(u => u.email === email)\n\n      if (existingUser) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني مسجل بالفعل' }\n        }\n      }\n\n      const newUser: User = {\n        id: Date.now().toString(),\n        email,\n        full_name: options?.data?.full_name,\n        created_at: new Date().toISOString()\n      }\n\n      users.push(newUser)\n      saveUsers(users)\n      setCurrentUser(newUser)\n\n      return { data: { user: newUser }, error: null }\n    }\n  },\n\n  from: (table: string) => ({\n    select: (columns?: string) => ({\n      eq: (column: string, value: any) => ({\n        single: () => Promise.resolve({ data: null, error: null })\n      }),\n      order: (column: string) => Promise.resolve({ data: [], error: null })\n    }),\n    insert: (data: any) => Promise.resolve({ data: null, error: null }),\n    update: (data: any) => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    delete: () => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    upsert: (data: any) => ({\n      select: () => Promise.resolve({ data: null, error: null })\n    })\n  })\n}\n\n// For client-side operations\nexport const getSupabase = async () => {\n  initializeDemoUsers()\n  return mockSupabase\n}\n\n// For server-side operations\nexport const createServerClient = async () => {\n  return mockSupabase\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WorshipRecord {\n  id: string\n  user_id: string\n  date: string\n  sleep_on_time: boolean\n  wake_up_on_time: boolean\n  night_prayer: boolean\n  fajr_prayer: boolean\n  quran_reading: boolean\n  istighfar_200: boolean\n  salawat_200: boolean\n  tasbih_100: boolean\n  la_ilaha_illa_allah_100: boolean\n  la_ilaha_illa_anta_50: boolean\n  morning_exercise: boolean\n  shower: boolean\n  duha_prayer: boolean\n  dhuhr_prayer: boolean\n  asr_prayer: boolean\n  maghrib_prayer: boolean\n  isha_prayer: boolean\n  slept_after_fajr: boolean\n  worship_percentage: number\n  spiritual_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LearningRecord {\n  id: string\n  user_id: string\n  date: string\n  laravel_time: number\n  laravel_topic?: string\n  laravel_progress: number\n  laravel_rating: number\n  nodejs_time: number\n  nodejs_topic?: string\n  nodejs_progress: number\n  nextjs_time: number\n  nextjs_topic?: string\n  nextjs_progress: number\n  mean_rating: number\n  flutter_time: number\n  flutter_topic?: string\n  flutter_progress: number\n  database_time: number\n  database_type?: string\n  database_progress: number\n  total_hours: number\n  key_learnings?: string\n  challenges?: string\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TemplateRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_time: number\n  shopify_features?: string\n  shopify_progress: number\n  shopify_issues?: string\n  shopify_solutions?: string\n  salla_progress: number\n  salla_notes?: string\n  wordpress_progress: number\n  wordpress_notes?: string\n  platform_api?: string\n  platform_functions?: string\n  platform_testing: boolean\n  wholesale_prices?: string\n  expected_revenue: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AppRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_functions?: string\n  shopify_integration: string\n  shopify_api_test: string\n  shopify_status: string\n  salla_functions?: string\n  salla_integration: string\n  salla_api_test: string\n  salla_status: string\n  wordpress_functions?: string\n  wordpress_integration: string\n  wordpress_api_test: string\n  wordpress_status: string\n  future_app_ideas?: string\n  future_app_planning?: string\n  future_app_features?: string\n  integration_issues?: string\n  price_updates?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ProjectRecord {\n  id: string\n  user_id: string\n  date: string\n  period1_project?: string\n  period1_tasks?: string\n  period1_time: number\n  period2_project?: string\n  period2_tasks?: string\n  period2_time: number\n  dashsy_tasks?: string\n  dashsy_progress: number\n  dashsy_issues?: string\n  dashsy_solutions?: string\n  dashsy_tomorrow?: string\n  syriana_tasks?: string\n  syriana_progress: number\n  syriana_issues?: string\n  syriana_solutions?: string\n  syriana_tomorrow?: string\n  booking_tasks?: string\n  booking_progress: number\n  booking_issues?: string\n  booking_solutions?: string\n  booking_tomorrow?: string\n  total_hours: number\n  productivity_rating: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FinanceRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_revenue: number\n  salla_revenue: number\n  wordpress_revenue: number\n  apps_revenue: number\n  projects_revenue: number\n  total_revenue: number\n  daily_expenses: number\n  net_profit: number\n  monthly_cumulative: number\n  financial_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WeeklyRecord {\n  id: string\n  user_id: string\n  week_start: string\n  week_end: string\n  routine_days: number\n  worship_percentage: number\n  total_learning_hours: number\n  key_topics?: string\n  laravel_progress: number\n  mean_progress: number\n  flutter_progress: number\n  database_progress: number\n  shopify_progress: number\n  platform_progress: number\n  apps_progress: number\n  dashsy_progress: number\n  syriana_progress: number\n  booking_progress: number\n  weekly_revenue: number\n  main_challenges?: string\n  main_solutions?: string\n  next_week_plan?: string\n  overall_rating: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface DashboardRecord {\n  id: string\n  user_id: string\n  date: string\n  routine_status: 'green' | 'yellow' | 'red'\n  learning_progress: number\n  templates_progress: number\n  projects_progress: number\n  daily_revenue: number\n  monthly_routine_percentage: number\n  monthly_learning_hours: number\n  monthly_templates_progress: number\n  monthly_projects_progress: number\n  urgent_alerts?: string\n  quick_notes?: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAarD,mCAAmC;AACnC,MAAM,iBAAiB;IACrB,wCAAmC,OAAO,EAAE;;IAC5C,MAAM;AAER;AAEA,MAAM,YAAY,CAAC;IACjB,wCAAmC;;AAErC;AAEA,MAAM,iBAAiB;IACrB,wCAAmC,OAAO;;IAC1C,MAAM;AAER;AAEA,MAAM,iBAAiB,CAAC;IACtB,wCAAmC;;AAMrC;AAEA,sCAAsC;AACtC,MAAM,sBAAsB;IAC1B,wCAAmC;;IACnC,MAAM;AAkBR;AAEA,uBAAuB;AACvB,MAAM,eAAe;IACnB,MAAM;QACJ,SAAS;YACP,MAAM,OAAO;YACb,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,SAAS;YACP,eAAe;YACf,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,oBAAoB,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACjF,MAAM,QAAQ;YACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEzC,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAA6B;gBACjD;YACF;YAEA,gCAAgC;YAChC,eAAe;YACf,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,QAAQ,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAIxC;YACC,MAAM,QAAQ;YACd,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEjD,IAAI,cAAc;gBAChB,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAAgC;gBACpD;YACF;YAEA,MAAM,UAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB;gBACA,WAAW,SAAS,MAAM;gBAC1B,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,IAAI,CAAC;YACX,UAAU;YACV,eAAe;YAEf,OAAO;gBAAE,MAAM;oBAAE,MAAM;gBAAQ;gBAAG,OAAO;YAAK;QAChD;IACF;IAEA,MAAM,CAAC,QAAkB,CAAC;YACxB,QAAQ,CAAC,UAAqB,CAAC;oBAC7B,IAAI,CAAC,QAAgB,QAAe,CAAC;4BACnC,QAAQ,IAAM,QAAQ,OAAO,CAAC;oCAAE,MAAM;oCAAM,OAAO;gCAAK;wBAC1D,CAAC;oBACD,OAAO,CAAC,SAAmB,QAAQ,OAAO,CAAC;4BAAE,MAAM,EAAE;4BAAE,OAAO;wBAAK;gBACrE,CAAC;YACD,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACjE,QAAQ,CAAC,OAAc,CAAC;oBACtB,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,IAAM,CAAC;oBACb,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,CAAC,OAAc,CAAC;oBACtB,QAAQ,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAC1D,CAAC;QACH,CAAC;AACH;AAGO,MAAM,cAAc;IACzB;IACA,OAAO;AACT;AAGO,MAAM,qBAAqB;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/lib/database-setup.ts"], "sourcesContent": ["// Database setup utility\nimport { getSupabase } from './supabase'\n\nexport async function setupDatabase() {\n  try {\n    // For now, using localStorage mock system\n    console.log('Using localStorage mock database system')\n    return { success: true, message: 'Mock database ready' }\n\n  } catch (error) {\n    console.error('Database setup error:', error)\n    return { success: false, message: 'Database connection failed' }\n  }\n}\n\nexport async function createUserProfile(userId: string, email: string, fullName?: string) {\n  try {\n    // Mock user profile creation - already handled in auth\n    const userData = { id: userId, email, full_name: fullName }\n    return { success: true, data: userData }\n  } catch (error) {\n    console.error('Error in createUserProfile:', error)\n    return { success: false, error }\n  }\n}\n\nexport async function getUserProfile(userId: string) {\n  try {\n    // Get current user from localStorage\n    if (typeof window !== 'undefined') {\n      const currentUser = localStorage.getItem('tracking_current_user')\n      if (currentUser) {\n        const userData = JSON.parse(currentUser)\n        if (userData.id === userId) {\n          return { success: true, data: userData }\n        }\n      }\n    }\n\n    return { success: false, error: { message: 'User not found' } }\n  } catch (error) {\n    console.error('Error in getUserProfile:', error)\n    return { success: false, error }\n  }\n}\n\n// Initialize database on app start\nexport async function initializeApp() {\n  const dbStatus = await setupDatabase()\n  console.log('Database status:', dbStatus)\n  return dbStatus\n}\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAGlB,eAAe;IACpB,IAAI;QACF,0CAA0C;QAC1C,QAAQ,GAAG,CAAC;QACZ,OAAO;YAAE,SAAS;YAAM,SAAS;QAAsB;IAEzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;YAAE,SAAS;YAAO,SAAS;QAA6B;IACjE;AACF;AAEO,eAAe,kBAAkB,MAAc,EAAE,KAAa,EAAE,QAAiB;IACtF,IAAI;QACF,uDAAuD;QACvD,MAAM,WAAW;YAAE,IAAI;YAAQ;YAAO,WAAW;QAAS;QAC1D,OAAO;YAAE,SAAS;YAAM,MAAM;QAAS;IACzC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,eAAe,eAAe,MAAc;IACjD,IAAI;QACF,qCAAqC;QACrC,uCAAmC;;QAQnC;QAEA,OAAO;YAAE,SAAS;YAAO,OAAO;gBAAE,SAAS;YAAiB;QAAE;IAChE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,eAAe;IACpB,MAAM,WAAW,MAAM;IACvB,QAAQ,GAAG,CAAC,oBAAoB;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport { getSupabase } from '@/lib/supabase'\nimport { initializeApp, getUserProfile } from '@/lib/database-setup'\n\ninterface DashboardStats {\n  routineStatus: 'green' | 'yellow' | 'red'\n  learningProgress: number\n  templatesProgress: number\n  projectsProgress: number\n  dailyRevenue: number\n  monthlyRoutinePercentage: number\n  monthlyLearningHours: number\n  monthlyTemplatesProgress: number\n  monthlyProjectsProgress: number\n}\n\nexport default function DashboardPage() {\n  const [stats, setStats] = useState<DashboardStats>({\n    routineStatus: 'green',\n    learningProgress: 75,\n    templatesProgress: 60,\n    projectsProgress: 85,\n    dailyRevenue: 150,\n    monthlyRoutinePercentage: 80,\n    monthlyLearningHours: 120,\n    monthlyTemplatesProgress: 65,\n    monthlyProjectsProgress: 78,\n  })\n  const [loading, setLoading] = useState(true)\n  const [user, setUser] = useState<any>(null)\n  const [userProfile, setUserProfile] = useState<any>(null)\n  const router = useRouter()\n\n  useEffect(() => {\n    checkAuth()\n    initializeDatabase()\n  }, [])\n\n  const checkAuth = async () => {\n    try {\n      const supabase = await getSupabase()\n      const { data: { user }, error } = await supabase.auth.getUser()\n\n      if (error || !user) {\n        router.push('/login')\n        return\n      }\n\n      setUser(user)\n\n      // Get user profile\n      const profileResult = await getUserProfile(user.id)\n      if (profileResult.success) {\n        setUserProfile(profileResult.data)\n      }\n\n    } catch (error) {\n      console.error('Auth check error:', error)\n      router.push('/login')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const initializeDatabase = async () => {\n    const result = await initializeApp()\n    console.log('Database initialization:', result)\n  }\n\n  const handleSignOut = async () => {\n    try {\n      const supabase = await getSupabase()\n      await supabase.auth.signOut()\n      router.push('/login')\n    } catch (error) {\n      console.error('Sign out error:', error)\n    }\n  }\n\n\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'green': return 'bg-green-100 text-green-800'\n      case 'yellow': return 'bg-yellow-100 text-yellow-800'\n      case 'red': return 'bg-red-100 text-red-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getStatusEmoji = (status: string) => {\n    switch (status) {\n      case 'green': return '🟢'\n      case 'yellow': return '🟡'\n      case 'red': return '🔴'\n      default: return '⚪'\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                🕌 نظام المتابعة الشامل\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <span className=\"text-sm text-gray-600\">\n                مرحباً، {userProfile?.full_name || user?.email || 'مستخدم'}\n              </span>\n              <button\n                onClick={handleSignOut}\n                className=\"text-sm text-red-600 hover:text-red-800\"\n              >\n                تسجيل الخروج\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        {/* Quick Stats */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">{getStatusEmoji(stats.routineStatus)}</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">حالة الروتين</p>\n                <p className={`text-sm px-2 py-1 rounded-full ${getStatusColor(stats.routineStatus)}`}>\n                  {stats.routineStatus === 'green' ? 'ممتاز' : \n                   stats.routineStatus === 'yellow' ? 'جيد' : 'يحتاج تحسين'}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">📚</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">تقدم التعلم</p>\n                <p className=\"text-2xl font-bold text-blue-600\">{stats.learningProgress}%</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">🛠️</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">تقدم المشاريع</p>\n                <p className=\"text-2xl font-bold text-green-600\">{stats.projectsProgress}%</p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">💰</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">الإيرادات اليومية</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">${stats.dailyRevenue}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Link href=\"/worship\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">🕌</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">العبادات والروتين</h3>\n              <p className=\"text-sm text-gray-600\">متابعة الصلوات والأذكار والروتين اليومي</p>\n            </div>\n          </Link>\n\n          <Link href=\"/learning\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">📚</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">التعلم</h3>\n              <p className=\"text-sm text-gray-600\">تتبع تعلم Laravel, MEAN Stack, Flutter</p>\n            </div>\n          </Link>\n\n          <Link href=\"/templates\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">🎨</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">القوالب والمنصات</h3>\n              <p className=\"text-sm text-gray-600\">تطوير قوالب Shopify, Salla, WordPress</p>\n            </div>\n          </Link>\n\n          <Link href=\"/apps\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">📱</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">تطبيقات المنصات</h3>\n              <p className=\"text-sm text-gray-600\">تطوير التطبيقات المرتبطة بالمنصات</p>\n            </div>\n          </Link>\n\n          <Link href=\"/projects\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">🏢</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">مشاريع الشركة</h3>\n              <p className=\"text-sm text-gray-600\">Dash-sy, Syriana Store, Booking-Sy</p>\n            </div>\n          </Link>\n\n          <Link href=\"/finance\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">💰</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">الإيرادات والمالية</h3>\n              <p className=\"text-sm text-gray-600\">تتبع الإيرادات والمصروفات</p>\n            </div>\n          </Link>\n\n          <Link href=\"/weekly\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">📊</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">المتابعة الأسبوعية</h3>\n              <p className=\"text-sm text-gray-600\">تقييم أسبوعي شامل للأداء</p>\n            </div>\n          </Link>\n\n          <Link href=\"/analytics\" className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow p-6 block\">\n            <div className=\"text-center\">\n              <span className=\"text-4xl mb-4 block\">📈</span>\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">التحليلات</h3>\n              <p className=\"text-sm text-gray-600\">رسوم بيانية وإحصائيات مفصلة</p>\n            </div>\n          </Link>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAoBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACjD,eAAe;QACf,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,cAAc;QACd,0BAA0B;QAC1B,sBAAsB;QACtB,0BAA0B;QAC1B,yBAAyB;IAC3B;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;YACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAE7D,IAAI,SAAS,CAAC,MAAM;gBAClB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,QAAQ;YAER,mBAAmB;YACnB,MAAM,gBAAgB,MAAM,CAAA,GAAA,+HAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,EAAE;YAClD,IAAI,cAAc,OAAO,EAAE;gBACzB,eAAe,cAAc,IAAI;YACnC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO,IAAI,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB;QACzB,MAAM,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,gBAAa,AAAD;QACjC,QAAQ,GAAG,CAAC,4BAA4B;IAC1C;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;YACjC,MAAM,SAAS,IAAI,CAAC,OAAO;YAC3B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;QACnC;IACF;IAIA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CAAwB;4CAC7B,aAAa,aAAa,MAAM,SAAS;;;;;;;kDAEpD,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAY,eAAe,MAAM,aAAa;;;;;;;;;;;sDAEhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAW,CAAC,+BAA+B,EAAE,eAAe,MAAM,aAAa,GAAG;8DAClF,MAAM,aAAa,KAAK,UAAU,UAClC,MAAM,aAAa,KAAK,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAMpD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAoC,MAAM,gBAAgB;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK9E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAqC,MAAM,gBAAgB;wDAAC;;;;;;;;;;;;;;;;;;;;;;;;0CAK/E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAqC;wDAAE,MAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAC9B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAQ,WAAU;0CAC3B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAY,WAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAW,WAAU;0CAC9B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAU,WAAU;0CAC7B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;0CAIzC,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAa,WAAU;0CAChC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;sDACtC,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD", "debugId": null}}]}