'use client'

import { useEffect, useState } from 'react'
import { getSupabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface WorshipData {
  id?: string
  date: string
  sleep_on_time: boolean
  wake_up_on_time: boolean
  night_prayer: boolean
  fajr_prayer: boolean
  quran_reading: boolean
  istighfar_200: boolean
  salawat_200: boolean
  tasbih_100: boolean
  la_ilaha_illa_allah_100: boolean
  la_ilaha_illa_anta_50: boolean
  morning_exercise: boolean
  shower: boolean
  duha_prayer: boolean
  dhuhr_prayer: boolean
  asr_prayer: boolean
  maghrib_prayer: boolean
  isha_prayer: boolean
  slept_after_fajr: boolean
  spiritual_notes: string
}

export default function WorshipPage() {
  const [user, setUser] = useState<any>(null)
  const [worshipData, setWorshipData] = useState<WorshipData>({
    date: new Date().toISOString().split('T')[0],
    sleep_on_time: false,
    wake_up_on_time: false,
    night_prayer: false,
    fajr_prayer: false,
    quran_reading: false,
    istighfar_200: false,
    salawat_200: false,
    tasbih_100: false,
    la_ilaha_illa_allah_100: false,
    la_ilaha_illa_anta_50: false,
    morning_exercise: false,
    shower: false,
    duha_prayer: false,
    dhuhr_prayer: false,
    asr_prayer: false,
    maghrib_prayer: false,
    isha_prayer: false,
    slept_after_fajr: false,
    spiritual_notes: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadWorshipData()
    }
  }, [user, worshipData.date])

  const checkUser = async () => {
    const supabase = await getSupabase()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
    } else {
      setUser(user)
    }
    setLoading(false)
  }

  const loadWorshipData = async () => {
    try {
      // Load from localStorage for now
      const storageKey = `worship_${user.id}_${worshipData.date}`
      const stored = localStorage.getItem(storageKey)

      if (stored) {
        const data = JSON.parse(stored)
        setWorshipData({
          id: data.id,
          date: data.date,
          sleep_on_time: data.sleep_on_time,
          wake_up_on_time: data.wake_up_on_time,
          night_prayer: data.night_prayer,
          fajr_prayer: data.fajr_prayer,
          quran_reading: data.quran_reading,
          istighfar_200: data.istighfar_200,
          salawat_200: data.salawat_200,
          tasbih_100: data.tasbih_100,
          la_ilaha_illa_allah_100: data.la_ilaha_illa_allah_100,
          la_ilaha_illa_anta_50: data.la_ilaha_illa_anta_50,
          morning_exercise: data.morning_exercise,
          shower: data.shower,
          duha_prayer: data.duha_prayer,
          dhuhr_prayer: data.dhuhr_prayer,
          asr_prayer: data.asr_prayer,
          maghrib_prayer: data.maghrib_prayer,
          isha_prayer: data.isha_prayer,
          slept_after_fajr: data.slept_after_fajr,
          spiritual_notes: data.spiritual_notes || ''
        })
      }
    } catch (error) {
      console.error('Error loading worship data:', error)
    }
  }

  const calculatePercentage = () => {
    const totalItems = 19 // عدد العناصر القابلة للتحقق
    const completedItems = [
      worshipData.sleep_on_time,
      worshipData.wake_up_on_time,
      worshipData.night_prayer,
      worshipData.fajr_prayer,
      worshipData.quran_reading,
      worshipData.istighfar_200,
      worshipData.salawat_200,
      worshipData.tasbih_100,
      worshipData.la_ilaha_illa_allah_100,
      worshipData.la_ilaha_illa_anta_50,
      worshipData.morning_exercise,
      worshipData.shower,
      worshipData.duha_prayer,
      worshipData.dhuhr_prayer,
      worshipData.asr_prayer,
      worshipData.maghrib_prayer,
      worshipData.isha_prayer,
      !worshipData.slept_after_fajr, // عكس لأن النوم بعد الفجر سلبي
    ].filter(Boolean).length

    return Math.round((completedItems / totalItems) * 100)
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const percentage = calculatePercentage()
      const dataToSave = {
        id: worshipData.id || `worship_${Date.now()}`,
        user_id: user.id,
        date: worshipData.date,
        sleep_on_time: worshipData.sleep_on_time,
        wake_up_on_time: worshipData.wake_up_on_time,
        night_prayer: worshipData.night_prayer,
        fajr_prayer: worshipData.fajr_prayer,
        quran_reading: worshipData.quran_reading,
        istighfar_200: worshipData.istighfar_200,
        salawat_200: worshipData.salawat_200,
        tasbih_100: worshipData.tasbih_100,
        la_ilaha_illa_allah_100: worshipData.la_ilaha_illa_allah_100,
        la_ilaha_illa_anta_50: worshipData.la_ilaha_illa_anta_50,
        morning_exercise: worshipData.morning_exercise,
        shower: worshipData.shower,
        duha_prayer: worshipData.duha_prayer,
        dhuhr_prayer: worshipData.dhuhr_prayer,
        asr_prayer: worshipData.asr_prayer,
        maghrib_prayer: worshipData.maghrib_prayer,
        isha_prayer: worshipData.isha_prayer,
        slept_after_fajr: worshipData.slept_after_fajr,
        worship_percentage: percentage,
        spiritual_notes: worshipData.spiritual_notes,
        created_at: new Date().toISOString()
      }

      // Save to localStorage
      const storageKey = `worship_${user.id}_${worshipData.date}`
      localStorage.setItem(storageKey, JSON.stringify(dataToSave))

      // Update state with new ID if it was a new record
      if (!worshipData.id) {
        setWorshipData(prev => ({ ...prev, id: dataToSave.id }))
      }

      alert('تم حفظ البيانات بنجاح!')
    } catch (error) {
      console.error('Error saving worship data:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handleCheckboxChange = (field: keyof WorshipData, value: boolean) => {
    setWorshipData(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  const percentage = calculatePercentage()

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للوحة الرئيسية
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                🕌 العبادات والروتين الشخصي
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                percentage >= 90 ? 'bg-green-100 text-green-800' :
                percentage >= 70 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {percentage}% مكتمل
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Date Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التاريخ
              </label>
              <input
                type="date"
                value={worshipData.date}
                onChange={(e) => setWorshipData(prev => ({ ...prev, date: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Worship Checklist */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Sleep & Wake */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">النوم والاستيقاظ</h3>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.sleep_on_time}
                    onChange={(e) => handleCheckboxChange('sleep_on_time', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>النوم في الوقت المحدد (10:30م)</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.wake_up_on_time}
                    onChange={(e) => handleCheckboxChange('wake_up_on_time', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>الاستيقاظ في الوقت المحدد (3:15ص)</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.slept_after_fajr}
                    onChange={(e) => handleCheckboxChange('slept_after_fajr', e.target.checked)}
                    className="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded ml-2"
                  />
                  <span className="text-red-600">نام بعد صلاة الفجر (سلبي)</span>
                </label>
              </div>

              {/* Prayers */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">الصلوات</h3>
                
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.night_prayer}
                    onChange={(e) => handleCheckboxChange('night_prayer', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>قيام الليل</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.fajr_prayer}
                    onChange={(e) => handleCheckboxChange('fajr_prayer', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>صلاة الفجر</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.duha_prayer}
                    onChange={(e) => handleCheckboxChange('duha_prayer', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>صلاة الضحى</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.dhuhr_prayer}
                    onChange={(e) => handleCheckboxChange('dhuhr_prayer', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>صلاة الظهر</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.asr_prayer}
                    onChange={(e) => handleCheckboxChange('asr_prayer', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>صلاة العصر</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.maghrib_prayer}
                    onChange={(e) => handleCheckboxChange('maghrib_prayer', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>صلاة المغرب</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.isha_prayer}
                    onChange={(e) => handleCheckboxChange('isha_prayer', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>صلاة العشاء</span>
                </label>
              </div>
            </div>

            {/* Dhikr & Quran */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">القرآن والأذكار</h3>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.quran_reading}
                    onChange={(e) => handleCheckboxChange('quran_reading', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>قراءة القرآن</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.istighfar_200}
                    onChange={(e) => handleCheckboxChange('istighfar_200', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>الاستغفار 200 مرة</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.salawat_200}
                    onChange={(e) => handleCheckboxChange('salawat_200', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>الصلاة على النبي 200 مرة</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.tasbih_100}
                    onChange={(e) => handleCheckboxChange('tasbih_100', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>التسبيح 100 مرة</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.la_ilaha_illa_allah_100}
                    onChange={(e) => handleCheckboxChange('la_ilaha_illa_allah_100', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>لا إله إلا الله 100 مرة</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.la_ilaha_illa_anta_50}
                    onChange={(e) => handleCheckboxChange('la_ilaha_illa_anta_50', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>لا إله إلا أنت سبحانك 50 مرة</span>
                </label>
              </div>

              {/* Physical Activities */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">الأنشطة البدنية</h3>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.morning_exercise}
                    onChange={(e) => handleCheckboxChange('morning_exercise', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>الرياضة الصباحية</span>
                </label>

                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={worshipData.shower}
                    onChange={(e) => handleCheckboxChange('shower', e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded ml-2"
                  />
                  <span>الاستحمام</span>
                </label>
              </div>
            </div>

            {/* Spiritual Notes */}
            <div className="mt-8">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                ملاحظات روحانية
              </label>
              <textarea
                value={worshipData.spiritual_notes}
                onChange={(e) => setWorshipData(prev => ({ ...prev, spiritual_notes: e.target.value }))}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="اكتب ملاحظاتك الروحانية هنا..."
              />
            </div>

            <div className="mt-8 pt-6 border-t">
              <button
                onClick={handleSave}
                disabled={saving}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ البيانات'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
