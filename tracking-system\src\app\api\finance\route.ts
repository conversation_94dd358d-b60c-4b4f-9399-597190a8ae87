import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const date = searchParams.get('date')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    let query = supabase
      .from('finance_records')
      .select('*')
      .eq('user_id', userId)

    if (date) {
      query = query.eq('date', date)
    } else if (startDate && endDate) {
      query = query.gte('date', startDate).lte('date', endDate)
    }

    const { data, error } = await query.order('date', { ascending: false })

    if (error) {
      console.error('Error fetching finance records:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in GET /api/finance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      user_id,
      date,
      dashsy_revenue,
      syriana_revenue,
      booking_revenue,
      templates_revenue,
      apps_revenue,
      other_revenue,
      total_expenses,
      finance_notes
    } = body

    if (!user_id || !date) {
      return NextResponse.json({ error: 'User ID and date are required' }, { status: 400 })
    }

    // Calculate totals
    const total_revenue = (dashsy_revenue || 0) + (syriana_revenue || 0) + (booking_revenue || 0) + 
                         (templates_revenue || 0) + (apps_revenue || 0) + (other_revenue || 0)
    const net_profit = total_revenue - (total_expenses || 0)

    const financeData = {
      user_id,
      date,
      dashsy_revenue: dashsy_revenue || 0,
      syriana_revenue: syriana_revenue || 0,
      booking_revenue: booking_revenue || 0,
      templates_revenue: templates_revenue || 0,
      apps_revenue: apps_revenue || 0,
      other_revenue: other_revenue || 0,
      total_revenue,
      total_expenses: total_expenses || 0,
      net_profit,
      finance_notes: finance_notes || ''
    }

    const { data, error } = await supabase
      .from('finance_records')
      .insert(financeData)
      .select()
      .single()

    if (error) {
      console.error('Error creating finance record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/finance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      user_id,
      date,
      dashsy_revenue,
      syriana_revenue,
      booking_revenue,
      templates_revenue,
      apps_revenue,
      other_revenue,
      total_expenses,
      finance_notes
    } = body

    if (!id || !user_id) {
      return NextResponse.json({ error: 'ID and User ID are required' }, { status: 400 })
    }

    // Calculate totals
    const total_revenue = (dashsy_revenue || 0) + (syriana_revenue || 0) + (booking_revenue || 0) + 
                         (templates_revenue || 0) + (apps_revenue || 0) + (other_revenue || 0)
    const net_profit = total_revenue - (total_expenses || 0)

    const financeData = {
      date,
      dashsy_revenue: dashsy_revenue || 0,
      syriana_revenue: syriana_revenue || 0,
      booking_revenue: booking_revenue || 0,
      templates_revenue: templates_revenue || 0,
      apps_revenue: apps_revenue || 0,
      other_revenue: other_revenue || 0,
      total_revenue,
      total_expenses: total_expenses || 0,
      net_profit,
      finance_notes: finance_notes || ''
    }

    const { data, error } = await supabase
      .from('finance_records')
      .update(financeData)
      .eq('id', id)
      .eq('user_id', user_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating finance record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in PUT /api/finance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const userId = searchParams.get('user_id')

    if (!id || !userId) {
      return NextResponse.json({ error: 'ID and User ID are required' }, { status: 400 })
    }

    const { error } = await supabase
      .from('finance_records')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting finance record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'Finance record deleted successfully' })
  } catch (error) {
    console.error('Error in DELETE /api/finance:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
