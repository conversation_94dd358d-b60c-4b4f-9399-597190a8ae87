التاريخ,<PERSON><PERSON> - الوقت (دقائق),<PERSON><PERSON> - الموضوع المدروس,<PERSON><PERSON> - التقدم %,<PERSON><PERSON> - التقييم (1-10),Node.js - الوقت (دقائق),Node.js - الموضوع المدروس,Node.js - التقدم %,Next.js - الوقت (دقائق),Next.js - الموضوع المدروس,Next.js - التقدم %,ME<PERSON> Stack - التقييم (1-10),Flutter - الوقت (دقائق),Flutter - الموضوع المدروس,Flutter - التقدم %,قواعد البيانات - الوقت (دقائق),قواعد البيانات - النوع المدروس,قواعد البيانات - التقدم %,إجمالي ساعات التعلم اليومية,أهم ما تعلمته اليوم,التحديات والصعوبات,خطة التعلم للغد
2024-01-01,45,Eloquent ORM,15%,8,30,Express.js Middleware,10%,25,API Routes,12%,7,20,Widget Development,8%,30,MySQL Optimization,20%,2.5,تعلمت كيفية استخدام Eloquent relationships,صعوبة في فهم Many-to-Many relationships,التركيز على Laravel Policies
2024-01-02,,,,,,,,,,,,,,,,,,,,,,
2024-01-03,,,,,,,,,,,,,,,,,,,,,,
2024-01-04,,,,,,,,,,,,,,,,,,,,,,
2024-01-05,,,,,,,,,,,,,,,,,,,,,,
2024-01-06,,,,,,,,,,,,,,,,,,,,,,
2024-01-07,,,,,,,,,,,,,,,,,,,,,,
2024-01-08,,,,,,,,,,,,,,,,,,,,,,
2024-01-09,,,,,,,,,,,,,,,,,,,,,,
2024-01-10,,,,,,,,,,,,,,,,,,,,,,
2024-01-11,,,,,,,,,,,,,,,,,,,,,,
2024-01-12,,,,,,,,,,,,,,,,,,,,,,
2024-01-13,,,,,,,,,,,,,,,,,,,,,,
2024-01-14,,,,,,,,,,,,,,,,,,,,,,
2024-01-15,,,,,,,,,,,,,,,,,,,,,,
2024-01-16,,,,,,,,,,,,,,,,,,,,,,
2024-01-17,,,,,,,,,,,,,,,,,,,,,,
2024-01-18,,,,,,,,,,,,,,,,,,,,,,
2024-01-19,,,,,,,,,,,,,,,,,,,,,,
2024-01-20,,,,,,,,,,,,,,,,,,,,,,
2024-01-21,,,,,,,,,,,,,,,,,,,,,,
2024-01-22,,,,,,,,,,,,,,,,,,,,,,
2024-01-23,,,,,,,,,,,,,,,,,,,,,,
2024-01-24,,,,,,,,,,,,,,,,,,,,,,
2024-01-25,,,,,,,,,,,,,,,,,,,,,,
2024-01-26,,,,,,,,,,,,,,,,,,,,,,
2024-01-27,,,,,,,,,,,,,,,,,,,,,,
2024-01-28,,,,,,,,,,,,,,,,,,,,,,
2024-01-29,,,,,,,,,,,,,,,,,,,,,,
2024-01-30,,,,,,,,,,,,,,,,,,,,,,
2024-01-31,,,,,,,,,,,,,,,,,,,,,,
