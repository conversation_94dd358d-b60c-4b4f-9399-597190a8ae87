'use client'

import { useEffect, useState } from 'react'
import { getSupabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface TemplateData {
  id?: string
  date: string
  shopify_time: number
  salla_time: number
  wordpress_time: number
  shopify_progress: number
  salla_progress: number
  wordpress_progress: number
  shopify_templates: string
  salla_templates: string
  wordpress_templates: string
  template_notes: string
}

export default function TemplatesPage() {
  const [user, setUser] = useState<any>(null)
  const [templateData, setTemplateData] = useState<TemplateData>({
    date: new Date().toISOString().split('T')[0],
    shopify_time: 0,
    salla_time: 0,
    wordpress_time: 0,
    shopify_progress: 0,
    salla_progress: 0,
    wordpress_progress: 0,
    shopify_templates: '',
    salla_templates: '',
    wordpress_templates: '',
    template_notes: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadTemplateData()
    }
  }, [user, templateData.date])

  const checkUser = async () => {
    const supabase = await getSupabase()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
    } else {
      setUser(user)
    }
    setLoading(false)
  }

  const loadTemplateData = async () => {
    try {
      const { data, error } = await supabase
        .from('template_records')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', templateData.date)
        .single()

      if (data) {
        setTemplateData({
          id: data.id,
          date: data.date,
          shopify_time: data.shopify_time || 0,
          salla_time: data.salla_time || 0,
          wordpress_time: data.wordpress_time || 0,
          shopify_progress: data.shopify_progress || 0,
          salla_progress: data.salla_progress || 0,
          wordpress_progress: data.wordpress_progress || 0,
          shopify_templates: data.shopify_templates || '',
          salla_templates: data.salla_templates || '',
          wordpress_templates: data.wordpress_templates || '',
          template_notes: data.template_notes || ''
        })
      }
    } catch (error) {
      console.error('Error loading template data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateTotalHours = () => {
    return (
      templateData.shopify_time +
      templateData.salla_time +
      templateData.wordpress_time
    ) / 60 // Convert minutes to hours
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const totalHours = calculateTotalHours()
      const dataToSave = {
        user_id: user.id,
        date: templateData.date,
        shopify_time: templateData.shopify_time,
        salla_time: templateData.salla_time,
        wordpress_time: templateData.wordpress_time,
        shopify_progress: templateData.shopify_progress,
        salla_progress: templateData.salla_progress,
        wordpress_progress: templateData.wordpress_progress,
        shopify_templates: templateData.shopify_templates,
        salla_templates: templateData.salla_templates,
        wordpress_templates: templateData.wordpress_templates,
        total_hours: totalHours,
        template_notes: templateData.template_notes
      }

      if (templateData.id) {
        await supabase
          .from('template_records')
          .update(dataToSave)
          .eq('id', templateData.id)
      } else {
        const { data } = await supabase
          .from('template_records')
          .insert(dataToSave)
          .select()
          .single()
        
        if (data) {
          setTemplateData(prev => ({ ...prev, id: data.id }))
        }
      }

      alert('تم حفظ البيانات بنجاح!')
    } catch (error) {
      console.error('Error saving template data:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handleTimeChange = (field: keyof TemplateData, value: number) => {
    setTemplateData(prev => ({ ...prev, [field]: value }))
  }

  const handleProgressChange = (field: keyof TemplateData, value: number) => {
    setTemplateData(prev => ({ ...prev, [field]: value }))
  }

  const handleTemplatesChange = (field: keyof TemplateData, value: string) => {
    setTemplateData(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  const totalHours = calculateTotalHours()

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للوحة الرئيسية
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                🎨 القوالب والمنصات
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                إجمالي: {totalHours.toFixed(1)} ساعة
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Date Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التاريخ
              </label>
              <input
                type="date"
                value={templateData.date}
                onChange={(e) => setTemplateData(prev => ({ ...prev, date: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Template Platforms */}
            <div className="space-y-8">
              {/* Shopify */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🛍️</span>
                  Shopify Templates
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={templateData.shopify_time}
                      onChange={(e) => handleTimeChange('shopify_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={templateData.shopify_progress}
                      onChange={(e) => handleProgressChange('shopify_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      القوالب المطورة
                    </label>
                    <textarea
                      value={templateData.shopify_templates}
                      onChange={(e) => handleTemplatesChange('shopify_templates', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب أسماء القوالب..."
                    />
                  </div>
                </div>
              </div>

              {/* Salla */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🏪</span>
                  Salla Templates
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={templateData.salla_time}
                      onChange={(e) => handleTimeChange('salla_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={templateData.salla_progress}
                      onChange={(e) => handleProgressChange('salla_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      القوالب المطورة
                    </label>
                    <textarea
                      value={templateData.salla_templates}
                      onChange={(e) => handleTemplatesChange('salla_templates', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب أسماء القوالب..."
                    />
                  </div>
                </div>
              </div>

              {/* WordPress */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">📝</span>
                  WordPress Templates
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={templateData.wordpress_time}
                      onChange={(e) => handleTimeChange('wordpress_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={templateData.wordpress_progress}
                      onChange={(e) => handleProgressChange('wordpress_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      القوالب المطورة
                    </label>
                    <textarea
                      value={templateData.wordpress_templates}
                      onChange={(e) => handleTemplatesChange('wordpress_templates', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب أسماء القوالب..."
                    />
                  </div>
                </div>
              </div>

              {/* Template Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات القوالب
                </label>
                <textarea
                  value={templateData.template_notes}
                  onChange={(e) => setTemplateData(prev => ({ ...prev, template_notes: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب ملاحظاتك حول تطوير القوالب..."
                />
              </div>
            </div>

            <div className="mt-8 pt-6 border-t">
              <button
                onClick={handleSave}
                disabled={saving}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ البيانات'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
