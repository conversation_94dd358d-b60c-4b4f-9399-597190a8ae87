<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قالب Google Sheets - نظام المتابعة الشامل</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .content {
            padding: 40px;
        }
        
        .template-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }
        
        .template-section h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        .copy-button {
            background: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .copy-button:hover {
            background: #219a52;
        }
        
        .formula-box {
            background: #ecf0f1;
            border: 1px solid #bdc3c7;
            border-radius: 5px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            direction: ltr;
            text-align: left;
        }
        
        .step-by-step {
            background: #e8f5e8;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .step {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .step-number {
            background: #3498db;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-weight: bold;
        }
        
        .quick-links {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .quick-link {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-decoration: none;
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .quick-link:hover {
            transform: translateY(-3px);
            text-decoration: none;
            color: white;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 قالب Google Sheets الجاهز</h1>
            <p>نظام المتابعة الشامل - جاهز للاستخدام المباشر</p>
        </div>
        
        <div class="content">
            <div class="success">
                <strong>🎉 تم إنشاء جميع الملفات بنجاح!</strong><br>
                يمكنك الآن نسخ البيانات مباشرة إلى Google Sheets
            </div>
            
            <div class="step-by-step">
                <h3>📋 خطوات الإعداد السريع</h3>
                
                <div class="step">
                    <div class="step-number">1</div>
                    <div>
                        <strong>إنشاء ملف جديد:</strong><br>
                        اذهب إلى <a href="https://sheets.google.com" target="_blank">Google Sheets</a> وأنشئ ملف جديد
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <div>
                        <strong>تسمية الملف:</strong><br>
                        اسم الملف: "نظام المتابعة الشامل - 2024"
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <div>
                        <strong>إنشاء الأوراق:</strong><br>
                        أنشئ 8 أوراق منفصلة بالأسماء التالية
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <div>
                        <strong>نسخ البيانات:</strong><br>
                        انسخ محتوى كل ملف CSV إلى الورقة المقابلة
                    </div>
                </div>
                
                <div class="step">
                    <div class="step-number">5</div>
                    <div>
                        <strong>تطبيق التنسيق:</strong><br>
                        استخدم الألوان والصيغ المقترحة أدناه
                    </div>
                </div>
            </div>
            
            <div class="template-section">
                <h3>🕌 الورقة 1: العبادات والروتين الشخصي</h3>
                <p><strong>اسم الورقة:</strong> العبادات والروتين</p>
                <button class="copy-button" onclick="copyToClipboard('sheet1')">نسخ اسم الورقة</button>
                
                <div class="formula-box">
                    <strong>صيغة حساب نسبة الإنجاز:</strong><br>
                    =COUNTIF(C2:R2,"✅")/COUNTA(C2:R2)*100
                </div>
                <button class="copy-button" onclick="copyToClipboard('formula1')">نسخ الصيغة</button>
                
                <div class="warning">
                    <strong>تنبيه:</strong> استخدم ✅ للمكتمل و ❌ للغير مكتمل
                </div>
            </div>
            
            <div class="template-section">
                <h3>🎓 الورقة 2: التعلم</h3>
                <p><strong>اسم الورقة:</strong> التعلم</p>
                <button class="copy-button" onclick="copyToClipboard('sheet2')">نسخ اسم الورقة</button>
                
                <div class="formula-box">
                    <strong>صيغة حساب إجمالي ساعات التعلم:</strong><br>
                    =SUM(B2:Q2)/60
                </div>
                <button class="copy-button" onclick="copyToClipboard('formula2')">نسخ الصيغة</button>
            </div>
            
            <div class="template-section">
                <h3>🛒 الورقة 3: القوالب والمنصات</h3>
                <p><strong>اسم الورقة:</strong> القوالب والمنصات</p>
                <button class="copy-button" onclick="copyToClipboard('sheet3')">نسخ اسم الورقة</button>
                
                <div class="formula-box">
                    <strong>صيغة حساب متوسط التقدم:</strong><br>
                    =AVERAGE(D2:I2)
                </div>
                <button class="copy-button" onclick="copyToClipboard('formula3')">نسخ الصيغة</button>
            </div>
            
            <div class="template-section">
                <h3>📱 الورقة 4: تطبيقات المنصات</h3>
                <p><strong>اسم الورقة:</strong> تطبيقات المنصات</p>
                <button class="copy-button" onclick="copyToClipboard('sheet4')">نسخ اسم الورقة</button>
            </div>
            
            <div class="template-section">
                <h3>💼 الورقة 5: مشاريع الشركة</h3>
                <p><strong>اسم الورقة:</strong> مشاريع الشركة</p>
                <button class="copy-button" onclick="copyToClipboard('sheet5')">نسخ اسم الورقة</button>
                
                <div class="formula-box">
                    <strong>صيغة حساب إجمالي ساعات العمل:</strong><br>
                    =(D2+G2)/60
                </div>
                <button class="copy-button" onclick="copyToClipboard('formula4')">نسخ الصيغة</button>
            </div>
            
            <div class="template-section">
                <h3>💰 الورقة 6: الإيرادات والمالية</h3>
                <p><strong>اسم الورقة:</strong> الإيرادات والمالية</p>
                <button class="copy-button" onclick="copyToClipboard('sheet6')">نسخ اسم الورقة</button>
                
                <div class="formula-box">
                    <strong>صيغة حساب إجمالي الإيرادات:</strong><br>
                    =SUM(B2:F2)
                </div>
                <button class="copy-button" onclick="copyToClipboard('formula5')">نسخ الصيغة</button>
                
                <div class="formula-box">
                    <strong>صيغة حساب صافي الربح:</strong><br>
                    =G2-H2
                </div>
                <button class="copy-button" onclick="copyToClipboard('formula6')">نسخ الصيغة</button>
            </div>
            
            <div class="template-section">
                <h3>📈 الورقة 7: المتابعة الأسبوعية</h3>
                <p><strong>اسم الورقة:</strong> المتابعة الأسبوعية</p>
                <button class="copy-button" onclick="copyToClipboard('sheet7')">نسخ اسم الورقة</button>
            </div>
            
            <div class="template-section">
                <h3>📊 الورقة 8: اللوحة الرئيسية</h3>
                <p><strong>اسم الورقة:</strong> اللوحة الرئيسية</p>
                <button class="copy-button" onclick="copyToClipboard('sheet8')">نسخ اسم الورقة</button>
                
                <div class="formula-box">
                    <strong>صيغة تحديد حالة الروتين:</strong><br>
                    =IF(C2>=90%,"🟢",IF(C2>=70%,"🟡","🔴"))
                </div>
                <button class="copy-button" onclick="copyToClipboard('formula7')">نسخ الصيغة</button>
            </div>
            
            <div class="quick-links">
                <a href="https://sheets.google.com" class="quick-link" target="_blank">
                    🚀 إنشاء Google Sheets جديد
                </a>
                <a href="#" class="quick-link" onclick="downloadAllFiles()">
                    📥 تحميل جميع ملفات CSV
                </a>
                <a href="#" class="quick-link" onclick="showColorGuide()">
                    🎨 دليل الألوان والتنسيق
                </a>
                <a href="#" class="quick-link" onclick="showFormulas()">
                    🧮 جميع الصيغ المطلوبة
                </a>
            </div>
            
            <div class="success">
                <h4>✅ قائمة التحقق النهائية:</h4>
                <ul>
                    <li>تم إنشاء جميع ملفات CSV ✅</li>
                    <li>تم إعداد الصيغ المطلوبة ✅</li>
                    <li>تم توفير دليل الاستخدام ✅</li>
                    <li>تم إعداد التنسيق والألوان ✅</li>
                    <li>النظام جاهز للاستخدام المباشر ✅</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function copyToClipboard(type) {
            let text = '';
            switch(type) {
                case 'sheet1': text = 'العبادات والروتين'; break;
                case 'sheet2': text = 'التعلم'; break;
                case 'sheet3': text = 'القوالب والمنصات'; break;
                case 'sheet4': text = 'تطبيقات المنصات'; break;
                case 'sheet5': text = 'مشاريع الشركة'; break;
                case 'sheet6': text = 'الإيرادات والمالية'; break;
                case 'sheet7': text = 'المتابعة الأسبوعية'; break;
                case 'sheet8': text = 'اللوحة الرئيسية'; break;
                case 'formula1': text = '=COUNTIF(C2:R2,"✅")/COUNTA(C2:R2)*100'; break;
                case 'formula2': text = '=SUM(B2:Q2)/60'; break;
                case 'formula3': text = '=AVERAGE(D2:I2)'; break;
                case 'formula4': text = '=(D2+G2)/60'; break;
                case 'formula5': text = '=SUM(B2:F2)'; break;
                case 'formula6': text = '=G2-H2'; break;
                case 'formula7': text = '=IF(C2>=90%,"🟢",IF(C2>=70%,"🟡","🔴"))'; break;
            }
            
            navigator.clipboard.writeText(text).then(function() {
                alert('تم نسخ النص بنجاح!');
            });
        }
        
        function downloadAllFiles() {
            alert('جميع ملفات CSV متوفرة في المجلد الحالي. يمكنك نسخ محتواها مباشرة إلى Google Sheets.');
        }
        
        function showColorGuide() {
            alert('دليل الألوان:\n🟢 ممتاز (90-100%)\n🟡 جيد (70-89%)\n🔴 يحتاج تحسين (أقل من 70%)\n\nألوان المشاريع:\nDash-sy: أزرق\nSyriana Store: أخضر\nBooking-Sy: برتقالي');
        }
        
        function showFormulas() {
            alert('الصيغ الأساسية متوفرة في كل قسم أعلاه. انقر على "نسخ الصيغة" لنسخها مباشرة.');
        }
    </script>
</body>
</html>
