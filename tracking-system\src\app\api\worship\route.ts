import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    let query = supabase
      .from('worship_records')
      .select('*')
      .eq('user_id', user.id)
      .order('date', { ascending: false })

    if (date) {
      query = query.eq('date', date)
    } else if (startDate && endDate) {
      query = query.gte('date', startDate).lte('date', endDate)
    }

    const { data, error } = await query

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    
    // Calculate worship percentage
    const totalItems = 19
    const completedItems = [
      body.sleep_on_time,
      body.wake_up_on_time,
      body.night_prayer,
      body.fajr_prayer,
      body.quran_reading,
      body.istighfar_200,
      body.salawat_200,
      body.tasbih_100,
      body.la_ilaha_illa_allah_100,
      body.la_ilaha_illa_anta_50,
      body.morning_exercise,
      body.shower,
      body.duha_prayer,
      body.dhuhr_prayer,
      body.asr_prayer,
      body.maghrib_prayer,
      body.isha_prayer,
      !body.slept_after_fajr, // Negative item
    ].filter(Boolean).length

    const worship_percentage = Math.round((completedItems / totalItems) * 100)

    const { data, error } = await supabase
      .from('worship_records')
      .insert({
        user_id: user.id,
        ...body,
        worship_percentage
      })
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { id, ...updateData } = body
    
    // Calculate worship percentage
    const totalItems = 19
    const completedItems = [
      updateData.sleep_on_time,
      updateData.wake_up_on_time,
      updateData.night_prayer,
      updateData.fajr_prayer,
      updateData.quran_reading,
      updateData.istighfar_200,
      updateData.salawat_200,
      updateData.tasbih_100,
      updateData.la_ilaha_illa_allah_100,
      updateData.la_ilaha_illa_anta_50,
      updateData.morning_exercise,
      updateData.shower,
      updateData.duha_prayer,
      updateData.dhuhr_prayer,
      updateData.asr_prayer,
      updateData.maghrib_prayer,
      updateData.isha_prayer,
      !updateData.slept_after_fajr, // Negative item
    ].filter(Boolean).length

    const worship_percentage = Math.round((completedItems / totalItems) * 100)

    const { data, error } = await supabase
      .from('worship_records')
      .update({
        ...updateData,
        worship_percentage
      })
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createClient()
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')

    if (!id) {
      return NextResponse.json({ error: 'ID is required' }, { status: 400 })
    }

    const { error } = await supabase
      .from('worship_records')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
