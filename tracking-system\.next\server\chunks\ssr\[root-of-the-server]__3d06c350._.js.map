{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/lib/supabase.ts"], "sourcesContent": ["// Temporary authentication system using localStorage\ninterface User {\n  id: string\n  email: string\n  full_name?: string\n  created_at: string\n}\n\ninterface AuthResponse {\n  data: { user: User | null }\n  error: any\n}\n\n// Mock database using localStorage\nconst getStoredUsers = (): User[] => {\n  if (typeof window === 'undefined') return []\n  const users = localStorage.getItem('tracking_users')\n  return users ? JSON.parse(users) : []\n}\n\nconst saveUsers = (users: User[]) => {\n  if (typeof window === 'undefined') return\n  localStorage.setItem('tracking_users', JSON.stringify(users))\n}\n\nconst getCurrentUser = (): User | null => {\n  if (typeof window === 'undefined') return null\n  const user = localStorage.getItem('tracking_current_user')\n  return user ? JSON.parse(user) : null\n}\n\nconst setCurrentUser = (user: User | null) => {\n  if (typeof window === 'undefined') return\n  if (user) {\n    localStorage.setItem('tracking_current_user', JSON.stringify(user))\n  } else {\n    localStorage.removeItem('tracking_current_user')\n  }\n}\n\n// Initialize demo users if none exist\nconst initializeDemoUsers = () => {\n  if (typeof window === 'undefined') return\n  const existingUsers = getStoredUsers()\n  if (existingUsers.length === 0) {\n    const demoUsers = [\n      {\n        id: 'demo-user-1',\n        email: '<EMAIL>',\n        full_name: 'مستخدم تجريبي',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'user-test-1',\n        email: '<EMAIL>',\n        full_name: 'سمير الكحلان',\n        created_at: new Date().toISOString()\n      }\n    ]\n    saveUsers(demoUsers)\n  }\n}\n\n// Mock Supabase client\nconst mockSupabase = {\n  auth: {\n    getUser: async (): Promise<AuthResponse> => {\n      const user = getCurrentUser()\n      return { data: { user }, error: null }\n    },\n\n    signOut: async () => {\n      setCurrentUser(null)\n      return { error: null }\n    },\n\n    signInWithPassword: async ({ email, password }: { email: string, password: string }) => {\n      const users = getStoredUsers()\n      const user = users.find(u => u.email === email)\n\n      if (!user) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني غير مسجل' }\n        }\n      }\n\n      // For demo, accept any password\n      setCurrentUser(user)\n      return { data: { user }, error: null }\n    },\n\n    signUp: async ({ email, password, options }: {\n      email: string,\n      password: string,\n      options?: { data?: { full_name?: string } }\n    }) => {\n      const users = getStoredUsers()\n      const existingUser = users.find(u => u.email === email)\n\n      if (existingUser) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني مسجل بالفعل' }\n        }\n      }\n\n      const newUser: User = {\n        id: Date.now().toString(),\n        email,\n        full_name: options?.data?.full_name,\n        created_at: new Date().toISOString()\n      }\n\n      users.push(newUser)\n      saveUsers(users)\n      setCurrentUser(newUser)\n\n      return { data: { user: newUser }, error: null }\n    }\n  },\n\n  from: (table: string) => ({\n    select: (columns?: string) => ({\n      eq: (column: string, value: any) => ({\n        single: () => Promise.resolve({ data: null, error: null })\n      }),\n      order: (column: string) => Promise.resolve({ data: [], error: null })\n    }),\n    insert: (data: any) => Promise.resolve({ data: null, error: null }),\n    update: (data: any) => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    delete: () => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    upsert: (data: any) => ({\n      select: () => Promise.resolve({ data: null, error: null })\n    })\n  })\n}\n\n// For client-side operations\nexport const getSupabase = async () => {\n  initializeDemoUsers()\n  return mockSupabase\n}\n\n// For server-side operations\nexport const createServerClient = async () => {\n  return mockSupabase\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WorshipRecord {\n  id: string\n  user_id: string\n  date: string\n  sleep_on_time: boolean\n  wake_up_on_time: boolean\n  night_prayer: boolean\n  fajr_prayer: boolean\n  quran_reading: boolean\n  istighfar_200: boolean\n  salawat_200: boolean\n  tasbih_100: boolean\n  la_ilaha_illa_allah_100: boolean\n  la_ilaha_illa_anta_50: boolean\n  morning_exercise: boolean\n  shower: boolean\n  duha_prayer: boolean\n  dhuhr_prayer: boolean\n  asr_prayer: boolean\n  maghrib_prayer: boolean\n  isha_prayer: boolean\n  slept_after_fajr: boolean\n  worship_percentage: number\n  spiritual_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LearningRecord {\n  id: string\n  user_id: string\n  date: string\n  laravel_time: number\n  laravel_topic?: string\n  laravel_progress: number\n  laravel_rating: number\n  nodejs_time: number\n  nodejs_topic?: string\n  nodejs_progress: number\n  nextjs_time: number\n  nextjs_topic?: string\n  nextjs_progress: number\n  mean_rating: number\n  flutter_time: number\n  flutter_topic?: string\n  flutter_progress: number\n  database_time: number\n  database_type?: string\n  database_progress: number\n  total_hours: number\n  key_learnings?: string\n  challenges?: string\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TemplateRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_time: number\n  shopify_features?: string\n  shopify_progress: number\n  shopify_issues?: string\n  shopify_solutions?: string\n  salla_progress: number\n  salla_notes?: string\n  wordpress_progress: number\n  wordpress_notes?: string\n  platform_api?: string\n  platform_functions?: string\n  platform_testing: boolean\n  wholesale_prices?: string\n  expected_revenue: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AppRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_functions?: string\n  shopify_integration: string\n  shopify_api_test: string\n  shopify_status: string\n  salla_functions?: string\n  salla_integration: string\n  salla_api_test: string\n  salla_status: string\n  wordpress_functions?: string\n  wordpress_integration: string\n  wordpress_api_test: string\n  wordpress_status: string\n  future_app_ideas?: string\n  future_app_planning?: string\n  future_app_features?: string\n  integration_issues?: string\n  price_updates?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ProjectRecord {\n  id: string\n  user_id: string\n  date: string\n  period1_project?: string\n  period1_tasks?: string\n  period1_time: number\n  period2_project?: string\n  period2_tasks?: string\n  period2_time: number\n  dashsy_tasks?: string\n  dashsy_progress: number\n  dashsy_issues?: string\n  dashsy_solutions?: string\n  dashsy_tomorrow?: string\n  syriana_tasks?: string\n  syriana_progress: number\n  syriana_issues?: string\n  syriana_solutions?: string\n  syriana_tomorrow?: string\n  booking_tasks?: string\n  booking_progress: number\n  booking_issues?: string\n  booking_solutions?: string\n  booking_tomorrow?: string\n  total_hours: number\n  productivity_rating: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FinanceRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_revenue: number\n  salla_revenue: number\n  wordpress_revenue: number\n  apps_revenue: number\n  projects_revenue: number\n  total_revenue: number\n  daily_expenses: number\n  net_profit: number\n  monthly_cumulative: number\n  financial_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WeeklyRecord {\n  id: string\n  user_id: string\n  week_start: string\n  week_end: string\n  routine_days: number\n  worship_percentage: number\n  total_learning_hours: number\n  key_topics?: string\n  laravel_progress: number\n  mean_progress: number\n  flutter_progress: number\n  database_progress: number\n  shopify_progress: number\n  platform_progress: number\n  apps_progress: number\n  dashsy_progress: number\n  syriana_progress: number\n  booking_progress: number\n  weekly_revenue: number\n  main_challenges?: string\n  main_solutions?: string\n  next_week_plan?: string\n  overall_rating: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface DashboardRecord {\n  id: string\n  user_id: string\n  date: string\n  routine_status: 'green' | 'yellow' | 'red'\n  learning_progress: number\n  templates_progress: number\n  projects_progress: number\n  daily_revenue: number\n  monthly_routine_percentage: number\n  monthly_learning_hours: number\n  monthly_templates_progress: number\n  monthly_projects_progress: number\n  urgent_alerts?: string\n  quick_notes?: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAarD,mCAAmC;AACnC,MAAM,iBAAiB;IACrB,wCAAmC,OAAO,EAAE;;IAC5C,MAAM;AAER;AAEA,MAAM,YAAY,CAAC;IACjB,wCAAmC;;AAErC;AAEA,MAAM,iBAAiB;IACrB,wCAAmC,OAAO;;IAC1C,MAAM;AAER;AAEA,MAAM,iBAAiB,CAAC;IACtB,wCAAmC;;AAMrC;AAEA,sCAAsC;AACtC,MAAM,sBAAsB;IAC1B,wCAAmC;;IACnC,MAAM;AAkBR;AAEA,uBAAuB;AACvB,MAAM,eAAe;IACnB,MAAM;QACJ,SAAS;YACP,MAAM,OAAO;YACb,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,SAAS;YACP,eAAe;YACf,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,oBAAoB,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACjF,MAAM,QAAQ;YACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEzC,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAA6B;gBACjD;YACF;YAEA,gCAAgC;YAChC,eAAe;YACf,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,QAAQ,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAIxC;YACC,MAAM,QAAQ;YACd,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEjD,IAAI,cAAc;gBAChB,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAAgC;gBACpD;YACF;YAEA,MAAM,UAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB;gBACA,WAAW,SAAS,MAAM;gBAC1B,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,IAAI,CAAC;YACX,UAAU;YACV,eAAe;YAEf,OAAO;gBAAE,MAAM;oBAAE,MAAM;gBAAQ;gBAAG,OAAO;YAAK;QAChD;IACF;IAEA,MAAM,CAAC,QAAkB,CAAC;YACxB,QAAQ,CAAC,UAAqB,CAAC;oBAC7B,IAAI,CAAC,QAAgB,QAAe,CAAC;4BACnC,QAAQ,IAAM,QAAQ,OAAO,CAAC;oCAAE,MAAM;oCAAM,OAAO;gCAAK;wBAC1D,CAAC;oBACD,OAAO,CAAC,SAAmB,QAAQ,OAAO,CAAC;4BAAE,MAAM,EAAE;4BAAE,OAAO;wBAAK;gBACrE,CAAC;YACD,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACjE,QAAQ,CAAC,OAAc,CAAC;oBACtB,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,IAAM,CAAC;oBACb,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,CAAC,OAAc,CAAC;oBACtB,QAAQ,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAC1D,CAAC;QACH,CAAC;AACH;AAGO,MAAM,cAAc;IACzB;IACA,OAAO;AACT;AAGO,MAAM,qBAAqB;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/finance/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { getSupabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\ninterface FinanceData {\n  id?: string\n  date: string\n  dashsy_revenue: number\n  syriana_revenue: number\n  booking_revenue: number\n  templates_revenue: number\n  apps_revenue: number\n  other_revenue: number\n  total_expenses: number\n  net_profit: number\n  finance_notes: string\n}\n\nexport default function FinancePage() {\n  const [user, setUser] = useState<any>(null)\n  const [financeData, setFinanceData] = useState<FinanceData>({\n    date: new Date().toISOString().split('T')[0],\n    dashsy_revenue: 0,\n    syriana_revenue: 0,\n    booking_revenue: 0,\n    templates_revenue: 0,\n    apps_revenue: 0,\n    other_revenue: 0,\n    total_expenses: 0,\n    net_profit: 0,\n    finance_notes: ''\n  })\n  const [loading, setLoading] = useState(true)\n  const [saving, setSaving] = useState(false)\n  const router = useRouter()\n\n  useEffect(() => {\n    checkUser()\n  }, [])\n\n  useEffect(() => {\n    if (user) {\n      loadFinanceData()\n    }\n  }, [user, financeData.date])\n\n  const checkUser = async () => {\n    const supabase = await getSupabase()\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) {\n      router.push('/login')\n    } else {\n      setUser(user)\n    }\n    setLoading(false)\n  }\n\n  const loadFinanceData = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('finance_records')\n        .select('*')\n        .eq('user_id', user.id)\n        .eq('date', financeData.date)\n        .single()\n\n      if (data) {\n        setFinanceData({\n          id: data.id,\n          date: data.date,\n          dashsy_revenue: data.dashsy_revenue || 0,\n          syriana_revenue: data.syriana_revenue || 0,\n          booking_revenue: data.booking_revenue || 0,\n          templates_revenue: data.templates_revenue || 0,\n          apps_revenue: data.apps_revenue || 0,\n          other_revenue: data.other_revenue || 0,\n          total_expenses: data.total_expenses || 0,\n          net_profit: data.net_profit || 0,\n          finance_notes: data.finance_notes || ''\n        })\n      }\n    } catch (error) {\n      console.error('Error loading finance data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateTotalRevenue = () => {\n    return (\n      financeData.dashsy_revenue +\n      financeData.syriana_revenue +\n      financeData.booking_revenue +\n      financeData.templates_revenue +\n      financeData.apps_revenue +\n      financeData.other_revenue\n    )\n  }\n\n  const calculateNetProfit = () => {\n    return calculateTotalRevenue() - financeData.total_expenses\n  }\n\n  const handleSave = async () => {\n    setSaving(true)\n    try {\n      const totalRevenue = calculateTotalRevenue()\n      const netProfit = calculateNetProfit()\n      \n      const dataToSave = {\n        user_id: user.id,\n        date: financeData.date,\n        dashsy_revenue: financeData.dashsy_revenue,\n        syriana_revenue: financeData.syriana_revenue,\n        booking_revenue: financeData.booking_revenue,\n        templates_revenue: financeData.templates_revenue,\n        apps_revenue: financeData.apps_revenue,\n        other_revenue: financeData.other_revenue,\n        total_revenue: totalRevenue,\n        total_expenses: financeData.total_expenses,\n        net_profit: netProfit,\n        finance_notes: financeData.finance_notes\n      }\n\n      if (financeData.id) {\n        await supabase\n          .from('finance_records')\n          .update(dataToSave)\n          .eq('id', financeData.id)\n      } else {\n        const { data } = await supabase\n          .from('finance_records')\n          .insert(dataToSave)\n          .select()\n          .single()\n        \n        if (data) {\n          setFinanceData(prev => ({ ...prev, id: data.id }))\n        }\n      }\n\n      alert('تم حفظ البيانات بنجاح!')\n    } catch (error) {\n      console.error('Error saving finance data:', error)\n      alert('حدث خطأ أثناء حفظ البيانات')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleRevenueChange = (field: keyof FinanceData, value: number) => {\n    setFinanceData(prev => ({ ...prev, [field]: value }))\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  const totalRevenue = calculateTotalRevenue()\n  const netProfit = calculateNetProfit()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-blue-600 hover:text-blue-800 ml-4\">\n                ← العودة للوحة الرئيسية\n              </Link>\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                💰 الإيرادات والمالية\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <div className=\"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium\">\n                إجمالي: ${totalRevenue.toFixed(2)}\n              </div>\n              <div className={`px-3 py-1 rounded-full text-sm font-medium ${\n                netProfit >= 0 ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'\n              }`}>\n                صافي: ${netProfit.toFixed(2)}\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6\">\n            {/* Date Selector */}\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                التاريخ\n              </label>\n              <input\n                type=\"date\"\n                value={financeData.date}\n                onChange={(e) => setFinanceData(prev => ({ ...prev, date: e.target.value }))}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            {/* Revenue Sources */}\n            <div className=\"space-y-6\">\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">مصادر الإيرادات</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      🚀 Dash-sy ($)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      value={financeData.dashsy_revenue}\n                      onChange={(e) => handleRevenueChange('dashsy_revenue', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      🛒 Syriana Store ($)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      value={financeData.syriana_revenue}\n                      onChange={(e) => handleRevenueChange('syriana_revenue', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      📅 Booking-Sy ($)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      value={financeData.booking_revenue}\n                      onChange={(e) => handleRevenueChange('booking_revenue', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      🎨 القوالب ($)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      value={financeData.templates_revenue}\n                      onChange={(e) => handleRevenueChange('templates_revenue', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      📱 التطبيقات ($)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      value={financeData.apps_revenue}\n                      onChange={(e) => handleRevenueChange('apps_revenue', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      💼 إيرادات أخرى ($)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      step=\"0.01\"\n                      value={financeData.other_revenue}\n                      onChange={(e) => handleRevenueChange('other_revenue', parseFloat(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Expenses */}\n              <div className=\"border-t pt-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">المصروفات</h3>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                    💸 إجمالي المصروفات ($)\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    step=\"0.01\"\n                    value={financeData.total_expenses}\n                    onChange={(e) => handleRevenueChange('total_expenses', parseFloat(e.target.value) || 0)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n\n              {/* Summary */}\n              <div className=\"border-t pt-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الملخص المالي</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  <div className=\"bg-green-50 p-4 rounded-lg\">\n                    <p className=\"text-sm text-green-600 font-medium\">إجمالي الإيرادات</p>\n                    <p className=\"text-2xl font-bold text-green-800\">${totalRevenue.toFixed(2)}</p>\n                  </div>\n                  <div className=\"bg-red-50 p-4 rounded-lg\">\n                    <p className=\"text-sm text-red-600 font-medium\">إجمالي المصروفات</p>\n                    <p className=\"text-2xl font-bold text-red-800\">${financeData.total_expenses.toFixed(2)}</p>\n                  </div>\n                  <div className={`p-4 rounded-lg ${netProfit >= 0 ? 'bg-blue-50' : 'bg-red-50'}`}>\n                    <p className={`text-sm font-medium ${netProfit >= 0 ? 'text-blue-600' : 'text-red-600'}`}>\n                      صافي الربح\n                    </p>\n                    <p className={`text-2xl font-bold ${netProfit >= 0 ? 'text-blue-800' : 'text-red-800'}`}>\n                      ${netProfit.toFixed(2)}\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              {/* Finance Notes */}\n              <div className=\"border-t pt-6\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  ملاحظات مالية\n                </label>\n                <textarea\n                  value={financeData.finance_notes}\n                  onChange={(e) => setFinanceData(prev => ({ ...prev, finance_notes: e.target.value }))}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"اكتب ملاحظاتك المالية هنا...\"\n                />\n              </div>\n            </div>\n\n            <div className=\"mt-8 pt-6 border-t\">\n              <button\n                onClick={handleSave}\n                disabled={saving}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n              >\n                {saving ? 'جاري الحفظ...' : 'حفظ البيانات'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAqBe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,gBAAgB;QAChB,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;QACnB,cAAc;QACd,eAAe;QACf,gBAAgB;QAChB,YAAY;QACZ,eAAe;IACjB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM,YAAY,IAAI;KAAC;IAE3B,MAAM,YAAY;QAChB,MAAM,YAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;QACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,UAAS,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;QACd,OAAO;YACL,QAAQ;QACV;QACA,WAAW;IACb;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,mBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,EAAE,CAAC,QAAQ,YAAY,IAAI,EAC3B,MAAM;YAET,IAAI,MAAM;gBACR,eAAe;oBACb,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,gBAAgB,KAAK,cAAc,IAAI;oBACvC,iBAAiB,KAAK,eAAe,IAAI;oBACzC,iBAAiB,KAAK,eAAe,IAAI;oBACzC,mBAAmB,KAAK,iBAAiB,IAAI;oBAC7C,cAAc,KAAK,YAAY,IAAI;oBACnC,eAAe,KAAK,aAAa,IAAI;oBACrC,gBAAgB,KAAK,cAAc,IAAI;oBACvC,YAAY,KAAK,UAAU,IAAI;oBAC/B,eAAe,KAAK,aAAa,IAAI;gBACvC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,wBAAwB;QAC5B,OACE,YAAY,cAAc,GAC1B,YAAY,eAAe,GAC3B,YAAY,eAAe,GAC3B,YAAY,iBAAiB,GAC7B,YAAY,YAAY,GACxB,YAAY,aAAa;IAE7B;IAEA,MAAM,qBAAqB;QACzB,OAAO,0BAA0B,YAAY,cAAc;IAC7D;IAEA,MAAM,aAAa;QACjB,UAAU;QACV,IAAI;YACF,MAAM,eAAe;YACrB,MAAM,YAAY;YAElB,MAAM,aAAa;gBACjB,SAAS,KAAK,EAAE;gBAChB,MAAM,YAAY,IAAI;gBACtB,gBAAgB,YAAY,cAAc;gBAC1C,iBAAiB,YAAY,eAAe;gBAC5C,iBAAiB,YAAY,eAAe;gBAC5C,mBAAmB,YAAY,iBAAiB;gBAChD,cAAc,YAAY,YAAY;gBACtC,eAAe,YAAY,aAAa;gBACxC,eAAe;gBACf,gBAAgB,YAAY,cAAc;gBAC1C,YAAY;gBACZ,eAAe,YAAY,aAAa;YAC1C;YAEA,IAAI,YAAY,EAAE,EAAE;gBAClB,MAAM,SACH,IAAI,CAAC,mBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,YAAY,EAAE;YAC5B,OAAO;gBACL,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,SACpB,IAAI,CAAC,mBACL,MAAM,CAAC,YACP,MAAM,GACN,MAAM;gBAET,IAAI,MAAM;oBACR,eAAe,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,IAAI,KAAK,EAAE;wBAAC,CAAC;gBAClD;YACF;YAEA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB,CAAC,OAA0B;QACrD,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACrD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,MAAM,eAAe;IACrB,MAAM,YAAY;IAElB,qBACE,8OAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAyC;;;;;;kDAG3E,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CAAyE;4CAC5E,aAAa,OAAO,CAAC;;;;;;;kDAEjC,8OAAC;wCAAI,WAAW,CAAC,2CAA2C,EAC1D,aAAa,IAAI,8BAA8B,2BAC/C;;4CAAE;4CACM,UAAU,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,YAAY,IAAI;wCACvB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC1E,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,OAAO,YAAY,cAAc;gEACjC,UAAU,CAAC,IAAM,oBAAoB,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEACrF,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,OAAO,YAAY,eAAe;gEAClC,UAAU,CAAC,IAAM,oBAAoB,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEACtF,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,OAAO,YAAY,eAAe;gEAClC,UAAU,CAAC,IAAM,oBAAoB,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEACtF,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,OAAO,YAAY,iBAAiB;gEACpC,UAAU,CAAC,IAAM,oBAAoB,qBAAqB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEACxF,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,OAAO,YAAY,YAAY;gEAC/B,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEACnF,WAAU;;;;;;;;;;;;kEAId,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,MAAK;gEACL,OAAO,YAAY,aAAa;gEAChC,UAAU,CAAC,IAAM,oBAAoB,iBAAiB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;gEACpF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,MAAK;wDACL,OAAO,YAAY,cAAc;wDACjC,UAAU,CAAC,IAAM,oBAAoB,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wDACrF,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAqC;;;;;;0EAClD,8OAAC;gEAAE,WAAU;;oEAAoC;oEAAE,aAAa,OAAO,CAAC;;;;;;;;;;;;;kEAE1E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAmC;;;;;;0EAChD,8OAAC;gEAAE,WAAU;;oEAAkC;oEAAE,YAAY,cAAc,CAAC,OAAO,CAAC;;;;;;;;;;;;;kEAEtF,8OAAC;wDAAI,WAAW,CAAC,eAAe,EAAE,aAAa,IAAI,eAAe,aAAa;;0EAC7E,8OAAC;gEAAE,WAAW,CAAC,oBAAoB,EAAE,aAAa,IAAI,kBAAkB,gBAAgB;0EAAE;;;;;;0EAG1F,8OAAC;gEAAE,WAAW,CAAC,mBAAmB,EAAE,aAAa,IAAI,kBAAkB,gBAAgB;;oEAAE;oEACrF,UAAU,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;kDAO5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,YAAY,aAAa;gDAChC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,eAAe,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACnF,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}]}