import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Test basic API functionality
    const envCheck = {
      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
      hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...',
    }

    return NextResponse.json({
      success: true,
      message: 'API is working correctly',
      environment: envCheck,
      timestamp: new Date().toISOString(),
      nodeVersion: process.version
    })

  } catch (error) {
    console.error('API test error:', error)
    return NextResponse.json({
      success: false,
      message: 'API test failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
