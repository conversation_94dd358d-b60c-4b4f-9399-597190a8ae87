import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const date = searchParams.get('date')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    let query = supabase
      .from('template_records')
      .select('*')
      .eq('user_id', userId)

    if (date) {
      query = query.eq('date', date)
    } else if (startDate && endDate) {
      query = query.gte('date', startDate).lte('date', endDate)
    }

    const { data, error } = await query.order('date', { ascending: false })

    if (error) {
      console.error('Error fetching template records:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in GET /api/templates:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      user_id,
      date,
      shopify_time,
      salla_time,
      wordpress_time,
      shopify_progress,
      salla_progress,
      wordpress_progress,
      shopify_templates,
      salla_templates,
      wordpress_templates,
      template_notes
    } = body

    if (!user_id || !date) {
      return NextResponse.json({ error: 'User ID and date are required' }, { status: 400 })
    }

    // Calculate total hours
    const total_hours = ((shopify_time || 0) + (salla_time || 0) + (wordpress_time || 0)) / 60

    const templateData = {
      user_id,
      date,
      shopify_time: shopify_time || 0,
      salla_time: salla_time || 0,
      wordpress_time: wordpress_time || 0,
      shopify_progress: shopify_progress || 0,
      salla_progress: salla_progress || 0,
      wordpress_progress: wordpress_progress || 0,
      shopify_templates: shopify_templates || '',
      salla_templates: salla_templates || '',
      wordpress_templates: wordpress_templates || '',
      total_hours,
      template_notes: template_notes || ''
    }

    const { data, error } = await supabase
      .from('template_records')
      .insert(templateData)
      .select()
      .single()

    if (error) {
      console.error('Error creating template record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/templates:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      user_id,
      date,
      shopify_time,
      salla_time,
      wordpress_time,
      shopify_progress,
      salla_progress,
      wordpress_progress,
      shopify_templates,
      salla_templates,
      wordpress_templates,
      template_notes
    } = body

    if (!id || !user_id) {
      return NextResponse.json({ error: 'ID and User ID are required' }, { status: 400 })
    }

    // Calculate total hours
    const total_hours = ((shopify_time || 0) + (salla_time || 0) + (wordpress_time || 0)) / 60

    const templateData = {
      date,
      shopify_time: shopify_time || 0,
      salla_time: salla_time || 0,
      wordpress_time: wordpress_time || 0,
      shopify_progress: shopify_progress || 0,
      salla_progress: salla_progress || 0,
      wordpress_progress: wordpress_progress || 0,
      shopify_templates: shopify_templates || '',
      salla_templates: salla_templates || '',
      wordpress_templates: wordpress_templates || '',
      total_hours,
      template_notes: template_notes || ''
    }

    const { data, error } = await supabase
      .from('template_records')
      .update(templateData)
      .eq('id', id)
      .eq('user_id', user_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating template record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in PUT /api/templates:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const userId = searchParams.get('user_id')

    if (!id || !userId) {
      return NextResponse.json({ error: 'ID and User ID are required' }, { status: 400 })
    }

    const { error } = await supabase
      .from('template_records')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting template record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'Template record deleted successfully' })
  } catch (error) {
    console.error('Error in DELETE /api/templates:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
