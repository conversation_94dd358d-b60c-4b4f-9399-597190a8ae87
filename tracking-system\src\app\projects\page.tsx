'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface ProjectData {
  id?: string
  date: string
  dashsy_time: number
  syriana_time: number
  booking_time: number
  dashsy_progress: number
  syriana_progress: number
  booking_progress: number
  dashsy_tasks: string
  syriana_tasks: string
  booking_tasks: string
  project_notes: string
}

export default function ProjectsPage() {
  const [user, setUser] = useState<any>(null)
  const [projectData, setProjectData] = useState<ProjectData>({
    date: new Date().toISOString().split('T')[0],
    dashsy_time: 0,
    syriana_time: 0,
    booking_time: 0,
    dashsy_progress: 0,
    syriana_progress: 0,
    booking_progress: 0,
    dashsy_tasks: '',
    syriana_tasks: '',
    booking_tasks: '',
    project_notes: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadProjectData()
    }
  }, [user, projectData.date])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
    } else {
      setUser(user)
    }
  }

  const loadProjectData = async () => {
    try {
      const { data, error } = await supabase
        .from('project_records')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', projectData.date)
        .single()

      if (data) {
        setProjectData({
          id: data.id,
          date: data.date,
          dashsy_time: data.dashsy_time || 0,
          syriana_time: data.syriana_time || 0,
          booking_time: data.booking_time || 0,
          dashsy_progress: data.dashsy_progress || 0,
          syriana_progress: data.syriana_progress || 0,
          booking_progress: data.booking_progress || 0,
          dashsy_tasks: data.dashsy_tasks || '',
          syriana_tasks: data.syriana_tasks || '',
          booking_tasks: data.booking_tasks || '',
          project_notes: data.project_notes || ''
        })
      }
    } catch (error) {
      console.error('Error loading project data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateTotalHours = () => {
    return (
      projectData.dashsy_time +
      projectData.syriana_time +
      projectData.booking_time
    ) / 60 // Convert minutes to hours
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const totalHours = calculateTotalHours()
      const dataToSave = {
        user_id: user.id,
        date: projectData.date,
        dashsy_time: projectData.dashsy_time,
        syriana_time: projectData.syriana_time,
        booking_time: projectData.booking_time,
        dashsy_progress: projectData.dashsy_progress,
        syriana_progress: projectData.syriana_progress,
        booking_progress: projectData.booking_progress,
        dashsy_tasks: projectData.dashsy_tasks,
        syriana_tasks: projectData.syriana_tasks,
        booking_tasks: projectData.booking_tasks,
        total_hours: totalHours,
        project_notes: projectData.project_notes
      }

      if (projectData.id) {
        await supabase
          .from('project_records')
          .update(dataToSave)
          .eq('id', projectData.id)
      } else {
        const { data } = await supabase
          .from('project_records')
          .insert(dataToSave)
          .select()
          .single()
        
        if (data) {
          setProjectData(prev => ({ ...prev, id: data.id }))
        }
      }

      alert('تم حفظ البيانات بنجاح!')
    } catch (error) {
      console.error('Error saving project data:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handleTimeChange = (field: keyof ProjectData, value: number) => {
    setProjectData(prev => ({ ...prev, [field]: value }))
  }

  const handleProgressChange = (field: keyof ProjectData, value: number) => {
    setProjectData(prev => ({ ...prev, [field]: value }))
  }

  const handleTasksChange = (field: keyof ProjectData, value: string) => {
    setProjectData(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  const totalHours = calculateTotalHours()

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للوحة الرئيسية
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                🏢 مشاريع الشركة
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                إجمالي: {totalHours.toFixed(1)} ساعة
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Date Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التاريخ
              </label>
              <input
                type="date"
                value={projectData.date}
                onChange={(e) => setProjectData(prev => ({ ...prev, date: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Projects */}
            <div className="space-y-8">
              {/* Dash-sy */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🚀</span>
                  Dash-sy
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={projectData.dashsy_time}
                      onChange={(e) => handleTimeChange('dashsy_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={projectData.dashsy_progress}
                      onChange={(e) => handleProgressChange('dashsy_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المهام المنجزة
                    </label>
                    <textarea
                      value={projectData.dashsy_tasks}
                      onChange={(e) => handleTasksChange('dashsy_tasks', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب المهام المنجزة..."
                    />
                  </div>
                </div>
              </div>

              {/* Syriana Store */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🛒</span>
                  Syriana Store
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={projectData.syriana_time}
                      onChange={(e) => handleTimeChange('syriana_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={projectData.syriana_progress}
                      onChange={(e) => handleProgressChange('syriana_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المهام المنجزة
                    </label>
                    <textarea
                      value={projectData.syriana_tasks}
                      onChange={(e) => handleTasksChange('syriana_tasks', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب المهام المنجزة..."
                    />
                  </div>
                </div>
              </div>

              {/* Booking-Sy */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">📅</span>
                  Booking-Sy
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={projectData.booking_time}
                      onChange={(e) => handleTimeChange('booking_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={projectData.booking_progress}
                      onChange={(e) => handleProgressChange('booking_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      المهام المنجزة
                    </label>
                    <textarea
                      value={projectData.booking_tasks}
                      onChange={(e) => handleTasksChange('booking_tasks', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب المهام المنجزة..."
                    />
                  </div>
                </div>
              </div>

              {/* Project Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات المشاريع
                </label>
                <textarea
                  value={projectData.project_notes}
                  onChange={(e) => setProjectData(prev => ({ ...prev, project_notes: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب ملاحظاتك حول المشاريع..."
                />
              </div>
            </div>

            <div className="mt-8 pt-6 border-t">
              <button
                onClick={handleSave}
                disabled={saving}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ البيانات'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
