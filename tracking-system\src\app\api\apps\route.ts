import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const date = searchParams.get('date')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    let query = supabase
      .from('app_records')
      .select('*')
      .eq('user_id', userId)

    if (date) {
      query = query.eq('date', date)
    } else if (startDate && endDate) {
      query = query.gte('date', startDate).lte('date', endDate)
    }

    const { data, error } = await query.order('date', { ascending: false })

    if (error) {
      console.error('Error fetching app records:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in GET /api/apps:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      user_id,
      date,
      shopify_app_time,
      salla_app_time,
      wordpress_plugin_time,
      mobile_app_time,
      shopify_app_progress,
      salla_app_progress,
      wordpress_plugin_progress,
      mobile_app_progress,
      shopify_apps,
      salla_apps,
      wordpress_plugins,
      mobile_apps,
      app_notes
    } = body

    if (!user_id || !date) {
      return NextResponse.json({ error: 'User ID and date are required' }, { status: 400 })
    }

    // Calculate total hours
    const total_hours = ((shopify_app_time || 0) + (salla_app_time || 0) + 
                        (wordpress_plugin_time || 0) + (mobile_app_time || 0)) / 60

    const appData = {
      user_id,
      date,
      shopify_app_time: shopify_app_time || 0,
      salla_app_time: salla_app_time || 0,
      wordpress_plugin_time: wordpress_plugin_time || 0,
      mobile_app_time: mobile_app_time || 0,
      shopify_app_progress: shopify_app_progress || 0,
      salla_app_progress: salla_app_progress || 0,
      wordpress_plugin_progress: wordpress_plugin_progress || 0,
      mobile_app_progress: mobile_app_progress || 0,
      shopify_apps: shopify_apps || '',
      salla_apps: salla_apps || '',
      wordpress_plugins: wordpress_plugins || '',
      mobile_apps: mobile_apps || '',
      total_hours,
      app_notes: app_notes || ''
    }

    const { data, error } = await supabase
      .from('app_records')
      .insert(appData)
      .select()
      .single()

    if (error) {
      console.error('Error creating app record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/apps:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      user_id,
      date,
      shopify_app_time,
      salla_app_time,
      wordpress_plugin_time,
      mobile_app_time,
      shopify_app_progress,
      salla_app_progress,
      wordpress_plugin_progress,
      mobile_app_progress,
      shopify_apps,
      salla_apps,
      wordpress_plugins,
      mobile_apps,
      app_notes
    } = body

    if (!id || !user_id) {
      return NextResponse.json({ error: 'ID and User ID are required' }, { status: 400 })
    }

    // Calculate total hours
    const total_hours = ((shopify_app_time || 0) + (salla_app_time || 0) + 
                        (wordpress_plugin_time || 0) + (mobile_app_time || 0)) / 60

    const appData = {
      date,
      shopify_app_time: shopify_app_time || 0,
      salla_app_time: salla_app_time || 0,
      wordpress_plugin_time: wordpress_plugin_time || 0,
      mobile_app_time: mobile_app_time || 0,
      shopify_app_progress: shopify_app_progress || 0,
      salla_app_progress: salla_app_progress || 0,
      wordpress_plugin_progress: wordpress_plugin_progress || 0,
      mobile_app_progress: mobile_app_progress || 0,
      shopify_apps: shopify_apps || '',
      salla_apps: salla_apps || '',
      wordpress_plugins: wordpress_plugins || '',
      mobile_apps: mobile_apps || '',
      total_hours,
      app_notes: app_notes || ''
    }

    const { data, error } = await supabase
      .from('app_records')
      .update(appData)
      .eq('id', id)
      .eq('user_id', user_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating app record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in PUT /api/apps:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const userId = searchParams.get('user_id')

    if (!id || !userId) {
      return NextResponse.json({ error: 'ID and User ID are required' }, { status: 400 })
    }

    const { error } = await supabase
      .from('app_records')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting app record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'App record deleted successfully' })
  } catch (error) {
    console.error('Error in DELETE /api/apps:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
