<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المتابعة الشامل - Google Sheets</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 40px;
        }
        
        .sheets-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }
        
        .sheet-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            border-left: 5px solid;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .sheet-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .sheet-card.worship { border-left-color: #27ae60; }
        .sheet-card.learning { border-left-color: #3498db; }
        .sheet-card.templates { border-left-color: #e74c3c; }
        .sheet-card.apps { border-left-color: #9b59b6; }
        .sheet-card.projects { border-left-color: #f39c12; }
        .sheet-card.finance { border-left-color: #1abc9c; }
        .sheet-card.weekly { border-left-color: #34495e; }
        .sheet-card.dashboard { border-left-color: #e67e22; }
        
        .sheet-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 1.4em;
        }
        
        .sheet-card p {
            color: #7f8c8d;
            line-height: 1.6;
            margin: 0;
        }
        
        .icon {
            font-size: 2em;
            margin-bottom: 15px;
            display: block;
        }
        
        .main-link {
            background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            text-align: center;
            text-decoration: none;
            display: block;
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 30px;
            transition: transform 0.3s ease;
        }
        
        .main-link:hover {
            transform: scale(1.02);
            text-decoration: none;
            color: white;
        }
        
        .instructions {
            background: #ecf0f1;
            padding: 25px;
            border-radius: 12px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            color: #2c3e50;
            margin-top: 0;
        }
        
        .instructions ol {
            color: #34495e;
            line-height: 1.8;
        }
        
        .schedule-overview {
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
        }
        
        .schedule-overview h3 {
            margin-top: 0;
            color: white;
        }
        
        .time-slot {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }
        
        .time-slot:last-child {
            border-bottom: none;
        }
        
        .time {
            font-weight: bold;
            min-width: 120px;
        }
        
        .activity {
            flex: 1;
            text-align: right;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕌 نظام المتابعة الشامل</h1>
            <p>جدولة زمنية متكاملة للعبادة والتعلم والعمل</p>
        </div>
        
        <div class="content">
            <!-- رابط الملف الرئيسي -->
            <a href="https://docs.google.com/spreadsheets/d/1example/edit#gid=0" class="main-link" target="_blank">
                📊 فتح ملف Google Sheets الرئيسي
            </a>
            
            <!-- نظرة عامة على الجدول الزمني -->
            <div class="schedule-overview">
                <h3>⏰ الجدول الزمني اليومي</h3>
                <div class="time-slot">
                    <span class="time">3:15ص - 5:30ص</span>
                    <span class="activity">قيام ليل + فجر + قرآن + أذكار</span>
                </div>
                <div class="time-slot">
                    <span class="time">5:30ص - 8:00ص</span>
                    <span class="activity">رياضة + دش + ضحى + قهوة</span>
                </div>
                <div class="time-slot">
                    <span class="time">8:00ص - 10:30ص</span>
                    <span class="activity">التعلم (Laravel + MEAN + Flutter + قواعد البيانات)</span>
                </div>
                <div class="time-slot">
                    <span class="time">10:30ص - 12:30م</span>
                    <span class="activity">العمل على القوالب والمنصات</span>
                </div>
                <div class="time-slot">
                    <span class="time">12:30م - 1:30م</span>
                    <span class="activity">ظهر + استراحة</span>
                </div>
                <div class="time-slot">
                    <span class="time">1:30م - 3:00م</span>
                    <span class="activity">العمل على مشاريع الشركة</span>
                </div>
                <div class="time-slot">
                    <span class="time">6:00م - 7:50م</span>
                    <span class="activity">مواصلة العمل على مشاريع الشركة</span>
                </div>
                <div class="time-slot">
                    <span class="time">10:30م</span>
                    <span class="activity">النوم</span>
                </div>
            </div>
            
            <!-- بطاقات الجداول -->
            <div class="sheets-grid">
                <div class="sheet-card worship">
                    <span class="icon">🕌</span>
                    <h3>جدول العبادات والروتين الشخصي</h3>
                    <p>متابعة الصلوات، قيام الليل، قراءة القرآن، الأذكار، والروتين الصباحي اليومي</p>
                </div>
                
                <div class="sheet-card learning">
                    <span class="icon">🎓</span>
                    <h3>جدول التعلم</h3>
                    <p>متابعة تعلم Laravel، MEAN Stack، Flutter، وقواعد البيانات من 8ص إلى 10:30ص</p>
                </div>
                
                <div class="sheet-card templates">
                    <span class="icon">🛒</span>
                    <h3>جدول القوالب والمنصات</h3>
                    <p>تطوير قوالب Shopify، Salla، WordPress ومنصة تجميع التجار</p>
                </div>
                
                <div class="sheet-card apps">
                    <span class="icon">📱</span>
                    <h3>جدول تطبيقات المنصات</h3>
                    <p>تطوير تطبيقات Shopify، Salla، WordPress والتطبيقات المستقبلية</p>
                </div>
                
                <div class="sheet-card projects">
                    <span class="icon">💼</span>
                    <h3>جدول مشاريع الشركة</h3>
                    <p>متابعة مشاريع Dash-sy، Syriana Store، Booking-Sy</p>
                </div>
                
                <div class="sheet-card finance">
                    <span class="icon">💰</span>
                    <h3>جدول الإيرادات والمالية</h3>
                    <p>متابعة الإيرادات من القوالب، التطبيقات، والمشاريع</p>
                </div>
                
                <div class="sheet-card weekly">
                    <span class="icon">📈</span>
                    <h3>جدول المتابعة الأسبوعية</h3>
                    <p>تقييم أسبوعي شامل لجميع الأنشطة والإنجازات</p>
                </div>
                
                <div class="sheet-card dashboard">
                    <span class="icon">📊</span>
                    <h3>اللوحة الرئيسية</h3>
                    <p>نظرة عامة سريعة على جميع الأنشطة والإحصائيات</p>
                </div>
            </div>
            
            <!-- تعليمات الاستخدام -->
            <div class="instructions">
                <h3>📋 تعليمات الاستخدام</h3>
                <ol>
                    <li>انقر على الرابط أعلاه لفتح ملف Google Sheets</li>
                    <li>انسخ الملف إلى حسابك الشخصي (File → Make a copy)</li>
                    <li>ابدأ بملء البيانات يومياً حسب الجدول الزمني</li>
                    <li>استخدم اللوحة الرئيسية للحصول على نظرة عامة سريعة</li>
                    <li>راجع التقرير الأسبوعي كل خميس</li>
                    <li>استخدم الرسوم البيانية لتتبع التقدم</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
