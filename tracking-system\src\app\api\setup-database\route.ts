import { NextResponse } from 'next/server'
import { getSupabase } from '@/lib/supabase'

export async function POST() {
  try {
    const supabase = await getSupabase()
    
    // Test if tables exist by trying to query them
    const tables = [
      'users',
      'worship_records', 
      'learning_records',
      'template_records',
      'app_records',
      'project_records',
      'finance_records',
      'weekly_reviews'
    ]
    
    const results = []
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('count')
          .limit(1)
        
        if (error) {
          results.push({ table, status: 'missing', error: error.message })
        } else {
          results.push({ table, status: 'exists' })
        }
      } catch (err) {
        results.push({ table, status: 'error', error: String(err) })
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Database check completed',
      tables: results,
      instructions: 'Please run the SQL files in your Supabase dashboard if tables are missing'
    })
    
  } catch (error) {
    console.error('Database setup error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Database setup failed',
        details: String(error)
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return POST()
}
