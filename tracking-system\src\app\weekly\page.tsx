'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface WeeklyData {
  id?: string
  week_start: string
  week_end: string
  worship_average: number
  learning_hours: number
  project_hours: number
  template_hours: number
  app_hours: number
  total_revenue: number
  net_profit: number
  goals_achieved: string
  challenges_faced: string
  lessons_learned: string
  next_week_goals: string
  overall_rating: number
  weekly_notes: string
}

export default function WeeklyPage() {
  const [user, setUser] = useState<any>(null)
  const [weeklyData, setWeeklyData] = useState<WeeklyData>({
    week_start: getWeekStart(),
    week_end: getWeekEnd(),
    worship_average: 0,
    learning_hours: 0,
    project_hours: 0,
    template_hours: 0,
    app_hours: 0,
    total_revenue: 0,
    net_profit: 0,
    goals_achieved: '',
    challenges_faced: '',
    lessons_learned: '',
    next_week_goals: '',
    overall_rating: 5,
    weekly_notes: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const router = useRouter()

  function getWeekStart() {
    const today = new Date()
    const day = today.getDay()
    const diff = today.getDate() - day + (day === 0 ? -6 : 1) // Adjust when day is Sunday
    const monday = new Date(today.setDate(diff))
    return monday.toISOString().split('T')[0]
  }

  function getWeekEnd() {
    const today = new Date()
    const day = today.getDay()
    const diff = today.getDate() - day + (day === 0 ? 0 : 7) // Adjust when day is Sunday
    const sunday = new Date(today.setDate(diff))
    return sunday.toISOString().split('T')[0]
  }

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadWeeklyData()
    }
  }, [user, weeklyData.week_start])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
    } else {
      setUser(user)
    }
  }

  const loadWeeklyData = async () => {
    try {
      const { data, error } = await supabase
        .from('weekly_records')
        .select('*')
        .eq('user_id', user.id)
        .eq('week_start', weeklyData.week_start)
        .single()

      if (data) {
        setWeeklyData({
          id: data.id,
          week_start: data.week_start,
          week_end: data.week_end,
          worship_average: data.worship_average || 0,
          learning_hours: data.learning_hours || 0,
          project_hours: data.project_hours || 0,
          template_hours: data.template_hours || 0,
          app_hours: data.app_hours || 0,
          total_revenue: data.total_revenue || 0,
          net_profit: data.net_profit || 0,
          goals_achieved: data.goals_achieved || '',
          challenges_faced: data.challenges_faced || '',
          lessons_learned: data.lessons_learned || '',
          next_week_goals: data.next_week_goals || '',
          overall_rating: data.overall_rating || 5,
          weekly_notes: data.weekly_notes || ''
        })
      } else {
        // Auto-calculate weekly stats from daily records
        await calculateWeeklyStats()
      }
    } catch (error) {
      console.error('Error loading weekly data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateWeeklyStats = async () => {
    try {
      // Get worship average
      const { data: worshipData } = await supabase
        .from('worship_records')
        .select('worship_percentage')
        .eq('user_id', user.id)
        .gte('date', weeklyData.week_start)
        .lte('date', weeklyData.week_end)

      // Get learning hours
      const { data: learningData } = await supabase
        .from('learning_records')
        .select('total_hours')
        .eq('user_id', user.id)
        .gte('date', weeklyData.week_start)
        .lte('date', weeklyData.week_end)

      // Get project hours
      const { data: projectData } = await supabase
        .from('project_records')
        .select('total_hours')
        .eq('user_id', user.id)
        .gte('date', weeklyData.week_start)
        .lte('date', weeklyData.week_end)

      // Get template hours
      const { data: templateData } = await supabase
        .from('template_records')
        .select('total_hours')
        .eq('user_id', user.id)
        .gte('date', weeklyData.week_start)
        .lte('date', weeklyData.week_end)

      // Get app hours
      const { data: appData } = await supabase
        .from('app_records')
        .select('total_hours')
        .eq('user_id', user.id)
        .gte('date', weeklyData.week_start)
        .lte('date', weeklyData.week_end)

      // Get financial data
      const { data: financeData } = await supabase
        .from('finance_records')
        .select('total_revenue, net_profit')
        .eq('user_id', user.id)
        .gte('date', weeklyData.week_start)
        .lte('date', weeklyData.week_end)

      const worshipAverage = worshipData?.length > 0 
        ? worshipData.reduce((sum, item) => sum + item.worship_percentage, 0) / worshipData.length 
        : 0

      const totalLearningHours = learningData?.reduce((sum, item) => sum + item.total_hours, 0) || 0
      const totalProjectHours = projectData?.reduce((sum, item) => sum + item.total_hours, 0) || 0
      const totalTemplateHours = templateData?.reduce((sum, item) => sum + item.total_hours, 0) || 0
      const totalAppHours = appData?.reduce((sum, item) => sum + item.total_hours, 0) || 0
      const totalRevenue = financeData?.reduce((sum, item) => sum + item.total_revenue, 0) || 0
      const totalProfit = financeData?.reduce((sum, item) => sum + item.net_profit, 0) || 0

      setWeeklyData(prev => ({
        ...prev,
        worship_average: Math.round(worshipAverage),
        learning_hours: Math.round(totalLearningHours * 10) / 10,
        project_hours: Math.round(totalProjectHours * 10) / 10,
        template_hours: Math.round(totalTemplateHours * 10) / 10,
        app_hours: Math.round(totalAppHours * 10) / 10,
        total_revenue: Math.round(totalRevenue * 100) / 100,
        net_profit: Math.round(totalProfit * 100) / 100
      }))
    } catch (error) {
      console.error('Error calculating weekly stats:', error)
    }
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const dataToSave = {
        user_id: user.id,
        week_start: weeklyData.week_start,
        week_end: weeklyData.week_end,
        worship_average: weeklyData.worship_average,
        learning_hours: weeklyData.learning_hours,
        project_hours: weeklyData.project_hours,
        template_hours: weeklyData.template_hours,
        app_hours: weeklyData.app_hours,
        total_revenue: weeklyData.total_revenue,
        net_profit: weeklyData.net_profit,
        goals_achieved: weeklyData.goals_achieved,
        challenges_faced: weeklyData.challenges_faced,
        lessons_learned: weeklyData.lessons_learned,
        next_week_goals: weeklyData.next_week_goals,
        overall_rating: weeklyData.overall_rating,
        weekly_notes: weeklyData.weekly_notes
      }

      if (weeklyData.id) {
        await supabase
          .from('weekly_records')
          .update(dataToSave)
          .eq('id', weeklyData.id)
      } else {
        const { data } = await supabase
          .from('weekly_records')
          .insert(dataToSave)
          .select()
          .single()
        
        if (data) {
          setWeeklyData(prev => ({ ...prev, id: data.id }))
        }
      }

      alert('تم حفظ البيانات بنجاح!')
    } catch (error) {
      console.error('Error saving weekly data:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handleFieldChange = (field: keyof WeeklyData, value: any) => {
    setWeeklyData(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  const getRatingColor = (rating: number) => {
    if (rating >= 8) return 'text-green-600'
    if (rating >= 6) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getRatingEmoji = (rating: number) => {
    if (rating >= 9) return '🌟'
    if (rating >= 8) return '😊'
    if (rating >= 6) return '😐'
    if (rating >= 4) return '😕'
    return '😞'
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للوحة الرئيسية
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                📊 المتابعة الأسبوعية
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="text-sm text-gray-600">
                {weeklyData.week_start} إلى {weeklyData.week_end}
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                weeklyData.overall_rating >= 8 ? 'bg-green-100 text-green-800' :
                weeklyData.overall_rating >= 6 ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {getRatingEmoji(weeklyData.overall_rating)} {weeklyData.overall_rating}/10
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Week Selector */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  بداية الأسبوع
                </label>
                <input
                  type="date"
                  value={weeklyData.week_start}
                  onChange={(e) => handleFieldChange('week_start', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  نهاية الأسبوع
                </label>
                <input
                  type="date"
                  value={weeklyData.week_end}
                  onChange={(e) => handleFieldChange('week_end', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* Auto-calculated Stats */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">الإحصائيات الأسبوعية</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-blue-50 p-4 rounded-lg text-center">
                  <p className="text-sm text-blue-600 font-medium">متوسط العبادات</p>
                  <p className="text-2xl font-bold text-blue-800">{weeklyData.worship_average}%</p>
                </div>
                <div className="bg-green-50 p-4 rounded-lg text-center">
                  <p className="text-sm text-green-600 font-medium">ساعات التعلم</p>
                  <p className="text-2xl font-bold text-green-800">{weeklyData.learning_hours}</p>
                </div>
                <div className="bg-purple-50 p-4 rounded-lg text-center">
                  <p className="text-sm text-purple-600 font-medium">ساعات المشاريع</p>
                  <p className="text-2xl font-bold text-purple-800">{weeklyData.project_hours}</p>
                </div>
                <div className="bg-yellow-50 p-4 rounded-lg text-center">
                  <p className="text-sm text-yellow-600 font-medium">صافي الربح</p>
                  <p className="text-2xl font-bold text-yellow-800">${weeklyData.net_profit}</p>
                </div>
              </div>
              <button
                onClick={calculateWeeklyStats}
                className="mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
              >
                إعادة حساب الإحصائيات
              </button>
            </div>

            {/* Weekly Review */}
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الأهداف المحققة هذا الأسبوع
                </label>
                <textarea
                  value={weeklyData.goals_achieved}
                  onChange={(e) => handleFieldChange('goals_achieved', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب الأهداف التي حققتها..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التحديات التي واجهتها
                </label>
                <textarea
                  value={weeklyData.challenges_faced}
                  onChange={(e) => handleFieldChange('challenges_faced', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب التحديات والصعوبات..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الدروس المستفادة
                </label>
                <textarea
                  value={weeklyData.lessons_learned}
                  onChange={(e) => handleFieldChange('lessons_learned', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب الدروس والخبرات المكتسبة..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  أهداف الأسبوع القادم
                </label>
                <textarea
                  value={weeklyData.next_week_goals}
                  onChange={(e) => handleFieldChange('next_week_goals', e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب أهدافك للأسبوع القادم..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التقييم العام للأسبوع (1-10)
                </label>
                <div className="flex items-center space-x-4 space-x-reverse">
                  <input
                    type="range"
                    min="1"
                    max="10"
                    value={weeklyData.overall_rating}
                    onChange={(e) => handleFieldChange('overall_rating', parseInt(e.target.value))}
                    className="flex-1"
                  />
                  <span className={`text-2xl font-bold ${getRatingColor(weeklyData.overall_rating)}`}>
                    {getRatingEmoji(weeklyData.overall_rating)} {weeklyData.overall_rating}
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات إضافية
                </label>
                <textarea
                  value={weeklyData.weekly_notes}
                  onChange={(e) => handleFieldChange('weekly_notes', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب أي ملاحظات إضافية..."
                />
              </div>
            </div>

            <div className="mt-8 pt-6 border-t">
              <button
                onClick={handleSave}
                disabled={saving}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ التقييم الأسبوعي'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
