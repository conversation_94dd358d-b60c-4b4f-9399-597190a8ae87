{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/lib/supabase.ts"], "sourcesContent": ["// Temporary authentication system using localStorage\ninterface User {\n  id: string\n  email: string\n  full_name?: string\n  created_at: string\n}\n\ninterface AuthResponse {\n  data: { user: User | null }\n  error: any\n}\n\n// Mock database using localStorage\nconst getStoredUsers = (): User[] => {\n  if (typeof window === 'undefined') return []\n  const users = localStorage.getItem('tracking_users')\n  return users ? JSON.parse(users) : []\n}\n\nconst saveUsers = (users: User[]) => {\n  if (typeof window === 'undefined') return\n  localStorage.setItem('tracking_users', JSON.stringify(users))\n}\n\nconst getCurrentUser = (): User | null => {\n  if (typeof window === 'undefined') return null\n  const user = localStorage.getItem('tracking_current_user')\n  return user ? JSON.parse(user) : null\n}\n\nconst setCurrentUser = (user: User | null) => {\n  if (typeof window === 'undefined') return\n  if (user) {\n    localStorage.setItem('tracking_current_user', JSON.stringify(user))\n  } else {\n    localStorage.removeItem('tracking_current_user')\n  }\n}\n\n// Initialize demo users if none exist\nconst initializeDemoUsers = () => {\n  if (typeof window === 'undefined') return\n  const existingUsers = getStoredUsers()\n  if (existingUsers.length === 0) {\n    const demoUsers = [\n      {\n        id: 'demo-user-1',\n        email: '<EMAIL>',\n        full_name: 'مستخدم تجريبي',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'user-test-1',\n        email: '<EMAIL>',\n        full_name: 'سمير الكحلان',\n        created_at: new Date().toISOString()\n      }\n    ]\n    saveUsers(demoUsers)\n  }\n}\n\n// Mock Supabase client\nconst mockSupabase = {\n  auth: {\n    getUser: async (): Promise<AuthResponse> => {\n      const user = getCurrentUser()\n      return { data: { user }, error: null }\n    },\n\n    signOut: async () => {\n      setCurrentUser(null)\n      return { error: null }\n    },\n\n    signInWithPassword: async ({ email, password }: { email: string, password: string }) => {\n      const users = getStoredUsers()\n      const user = users.find(u => u.email === email)\n\n      if (!user) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني غير مسجل' }\n        }\n      }\n\n      // For demo, accept any password\n      setCurrentUser(user)\n      return { data: { user }, error: null }\n    },\n\n    signUp: async ({ email, password, options }: {\n      email: string,\n      password: string,\n      options?: { data?: { full_name?: string } }\n    }) => {\n      const users = getStoredUsers()\n      const existingUser = users.find(u => u.email === email)\n\n      if (existingUser) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني مسجل بالفعل' }\n        }\n      }\n\n      const newUser: User = {\n        id: Date.now().toString(),\n        email,\n        full_name: options?.data?.full_name,\n        created_at: new Date().toISOString()\n      }\n\n      users.push(newUser)\n      saveUsers(users)\n      setCurrentUser(newUser)\n\n      return { data: { user: newUser }, error: null }\n    }\n  },\n\n  from: (table: string) => ({\n    select: (columns?: string) => ({\n      eq: (column: string, value: any) => ({\n        single: () => Promise.resolve({ data: null, error: null })\n      }),\n      order: (column: string) => Promise.resolve({ data: [], error: null })\n    }),\n    insert: (data: any) => Promise.resolve({ data: null, error: null }),\n    update: (data: any) => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    delete: () => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    upsert: (data: any) => ({\n      select: () => Promise.resolve({ data: null, error: null })\n    })\n  })\n}\n\n// For client-side operations\nexport const getSupabase = async () => {\n  initializeDemoUsers()\n  return mockSupabase\n}\n\n// For server-side operations\nexport const createServerClient = async () => {\n  return mockSupabase\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WorshipRecord {\n  id: string\n  user_id: string\n  date: string\n  sleep_on_time: boolean\n  wake_up_on_time: boolean\n  night_prayer: boolean\n  fajr_prayer: boolean\n  quran_reading: boolean\n  istighfar_200: boolean\n  salawat_200: boolean\n  tasbih_100: boolean\n  la_ilaha_illa_allah_100: boolean\n  la_ilaha_illa_anta_50: boolean\n  morning_exercise: boolean\n  shower: boolean\n  duha_prayer: boolean\n  dhuhr_prayer: boolean\n  asr_prayer: boolean\n  maghrib_prayer: boolean\n  isha_prayer: boolean\n  slept_after_fajr: boolean\n  worship_percentage: number\n  spiritual_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LearningRecord {\n  id: string\n  user_id: string\n  date: string\n  laravel_time: number\n  laravel_topic?: string\n  laravel_progress: number\n  laravel_rating: number\n  nodejs_time: number\n  nodejs_topic?: string\n  nodejs_progress: number\n  nextjs_time: number\n  nextjs_topic?: string\n  nextjs_progress: number\n  mean_rating: number\n  flutter_time: number\n  flutter_topic?: string\n  flutter_progress: number\n  database_time: number\n  database_type?: string\n  database_progress: number\n  total_hours: number\n  key_learnings?: string\n  challenges?: string\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TemplateRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_time: number\n  shopify_features?: string\n  shopify_progress: number\n  shopify_issues?: string\n  shopify_solutions?: string\n  salla_progress: number\n  salla_notes?: string\n  wordpress_progress: number\n  wordpress_notes?: string\n  platform_api?: string\n  platform_functions?: string\n  platform_testing: boolean\n  wholesale_prices?: string\n  expected_revenue: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AppRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_functions?: string\n  shopify_integration: string\n  shopify_api_test: string\n  shopify_status: string\n  salla_functions?: string\n  salla_integration: string\n  salla_api_test: string\n  salla_status: string\n  wordpress_functions?: string\n  wordpress_integration: string\n  wordpress_api_test: string\n  wordpress_status: string\n  future_app_ideas?: string\n  future_app_planning?: string\n  future_app_features?: string\n  integration_issues?: string\n  price_updates?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ProjectRecord {\n  id: string\n  user_id: string\n  date: string\n  period1_project?: string\n  period1_tasks?: string\n  period1_time: number\n  period2_project?: string\n  period2_tasks?: string\n  period2_time: number\n  dashsy_tasks?: string\n  dashsy_progress: number\n  dashsy_issues?: string\n  dashsy_solutions?: string\n  dashsy_tomorrow?: string\n  syriana_tasks?: string\n  syriana_progress: number\n  syriana_issues?: string\n  syriana_solutions?: string\n  syriana_tomorrow?: string\n  booking_tasks?: string\n  booking_progress: number\n  booking_issues?: string\n  booking_solutions?: string\n  booking_tomorrow?: string\n  total_hours: number\n  productivity_rating: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FinanceRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_revenue: number\n  salla_revenue: number\n  wordpress_revenue: number\n  apps_revenue: number\n  projects_revenue: number\n  total_revenue: number\n  daily_expenses: number\n  net_profit: number\n  monthly_cumulative: number\n  financial_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WeeklyRecord {\n  id: string\n  user_id: string\n  week_start: string\n  week_end: string\n  routine_days: number\n  worship_percentage: number\n  total_learning_hours: number\n  key_topics?: string\n  laravel_progress: number\n  mean_progress: number\n  flutter_progress: number\n  database_progress: number\n  shopify_progress: number\n  platform_progress: number\n  apps_progress: number\n  dashsy_progress: number\n  syriana_progress: number\n  booking_progress: number\n  weekly_revenue: number\n  main_challenges?: string\n  main_solutions?: string\n  next_week_plan?: string\n  overall_rating: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface DashboardRecord {\n  id: string\n  user_id: string\n  date: string\n  routine_status: 'green' | 'yellow' | 'red'\n  learning_progress: number\n  templates_progress: number\n  projects_progress: number\n  daily_revenue: number\n  monthly_routine_percentage: number\n  monthly_learning_hours: number\n  monthly_templates_progress: number\n  monthly_projects_progress: number\n  urgent_alerts?: string\n  quick_notes?: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAarD,mCAAmC;AACnC,MAAM,iBAAiB;IACrB,wCAAmC,OAAO,EAAE;;IAC5C,MAAM;AAER;AAEA,MAAM,YAAY,CAAC;IACjB,wCAAmC;;AAErC;AAEA,MAAM,iBAAiB;IACrB,wCAAmC,OAAO;;IAC1C,MAAM;AAER;AAEA,MAAM,iBAAiB,CAAC;IACtB,wCAAmC;;AAMrC;AAEA,sCAAsC;AACtC,MAAM,sBAAsB;IAC1B,wCAAmC;;IACnC,MAAM;AAkBR;AAEA,uBAAuB;AACvB,MAAM,eAAe;IACnB,MAAM;QACJ,SAAS;YACP,MAAM,OAAO;YACb,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,SAAS;YACP,eAAe;YACf,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,oBAAoB,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACjF,MAAM,QAAQ;YACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEzC,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAA6B;gBACjD;YACF;YAEA,gCAAgC;YAChC,eAAe;YACf,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,QAAQ,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAIxC;YACC,MAAM,QAAQ;YACd,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEjD,IAAI,cAAc;gBAChB,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAAgC;gBACpD;YACF;YAEA,MAAM,UAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB;gBACA,WAAW,SAAS,MAAM;gBAC1B,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,IAAI,CAAC;YACX,UAAU;YACV,eAAe;YAEf,OAAO;gBAAE,MAAM;oBAAE,MAAM;gBAAQ;gBAAG,OAAO;YAAK;QAChD;IACF;IAEA,MAAM,CAAC,QAAkB,CAAC;YACxB,QAAQ,CAAC,UAAqB,CAAC;oBAC7B,IAAI,CAAC,QAAgB,QAAe,CAAC;4BACnC,QAAQ,IAAM,QAAQ,OAAO,CAAC;oCAAE,MAAM;oCAAM,OAAO;gCAAK;wBAC1D,CAAC;oBACD,OAAO,CAAC,SAAmB,QAAQ,OAAO,CAAC;4BAAE,MAAM,EAAE;4BAAE,OAAO;wBAAK;gBACrE,CAAC;YACD,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACjE,QAAQ,CAAC,OAAc,CAAC;oBACtB,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,IAAM,CAAC;oBACb,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,CAAC,OAAc,CAAC;oBACtB,QAAQ,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAC1D,CAAC;QACH,CAAC;AACH;AAGO,MAAM,cAAc;IACzB;IACA,OAAO;AACT;AAGO,MAAM,qBAAqB;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/weekly/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { getSupabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\ninterface WeeklyData {\n  id?: string\n  week_start: string\n  week_end: string\n  worship_average: number\n  learning_hours: number\n  project_hours: number\n  template_hours: number\n  app_hours: number\n  total_revenue: number\n  net_profit: number\n  goals_achieved: string\n  challenges_faced: string\n  lessons_learned: string\n  next_week_goals: string\n  overall_rating: number\n  weekly_notes: string\n}\n\nexport default function WeeklyPage() {\n  const [user, setUser] = useState<any>(null)\n  const [weeklyData, setWeeklyData] = useState<WeeklyData>({\n    week_start: getWeekStart(),\n    week_end: getWeekEnd(),\n    worship_average: 0,\n    learning_hours: 0,\n    project_hours: 0,\n    template_hours: 0,\n    app_hours: 0,\n    total_revenue: 0,\n    net_profit: 0,\n    goals_achieved: '',\n    challenges_faced: '',\n    lessons_learned: '',\n    next_week_goals: '',\n    overall_rating: 5,\n    weekly_notes: ''\n  })\n  const [loading, setLoading] = useState(true)\n  const [saving, setSaving] = useState(false)\n  const router = useRouter()\n\n  function getWeekStart() {\n    const today = new Date()\n    const day = today.getDay()\n    const diff = today.getDate() - day + (day === 0 ? -6 : 1) // Adjust when day is Sunday\n    const monday = new Date(today.setDate(diff))\n    return monday.toISOString().split('T')[0]\n  }\n\n  function getWeekEnd() {\n    const today = new Date()\n    const day = today.getDay()\n    const diff = today.getDate() - day + (day === 0 ? 0 : 7) // Adjust when day is Sunday\n    const sunday = new Date(today.setDate(diff))\n    return sunday.toISOString().split('T')[0]\n  }\n\n  useEffect(() => {\n    checkUser()\n  }, [])\n\n  useEffect(() => {\n    if (user) {\n      loadWeeklyData()\n    }\n  }, [user, weeklyData.week_start])\n\n  const checkUser = async () => {\n    const supabase = await getSupabase()\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) {\n      router.push('/login')\n    } else {\n      setUser(user)\n    }\n    setLoading(false)\n  }\n\n  const loadWeeklyData = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('weekly_records')\n        .select('*')\n        .eq('user_id', user.id)\n        .eq('week_start', weeklyData.week_start)\n        .single()\n\n      if (data) {\n        setWeeklyData({\n          id: data.id,\n          week_start: data.week_start,\n          week_end: data.week_end,\n          worship_average: data.worship_average || 0,\n          learning_hours: data.learning_hours || 0,\n          project_hours: data.project_hours || 0,\n          template_hours: data.template_hours || 0,\n          app_hours: data.app_hours || 0,\n          total_revenue: data.total_revenue || 0,\n          net_profit: data.net_profit || 0,\n          goals_achieved: data.goals_achieved || '',\n          challenges_faced: data.challenges_faced || '',\n          lessons_learned: data.lessons_learned || '',\n          next_week_goals: data.next_week_goals || '',\n          overall_rating: data.overall_rating || 5,\n          weekly_notes: data.weekly_notes || ''\n        })\n      } else {\n        // Auto-calculate weekly stats from daily records\n        await calculateWeeklyStats()\n      }\n    } catch (error) {\n      console.error('Error loading weekly data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateWeeklyStats = async () => {\n    try {\n      // Get worship average\n      const { data: worshipData } = await supabase\n        .from('worship_records')\n        .select('worship_percentage')\n        .eq('user_id', user.id)\n        .gte('date', weeklyData.week_start)\n        .lte('date', weeklyData.week_end)\n\n      // Get learning hours\n      const { data: learningData } = await supabase\n        .from('learning_records')\n        .select('total_hours')\n        .eq('user_id', user.id)\n        .gte('date', weeklyData.week_start)\n        .lte('date', weeklyData.week_end)\n\n      // Get project hours\n      const { data: projectData } = await supabase\n        .from('project_records')\n        .select('total_hours')\n        .eq('user_id', user.id)\n        .gte('date', weeklyData.week_start)\n        .lte('date', weeklyData.week_end)\n\n      // Get template hours\n      const { data: templateData } = await supabase\n        .from('template_records')\n        .select('total_hours')\n        .eq('user_id', user.id)\n        .gte('date', weeklyData.week_start)\n        .lte('date', weeklyData.week_end)\n\n      // Get app hours\n      const { data: appData } = await supabase\n        .from('app_records')\n        .select('total_hours')\n        .eq('user_id', user.id)\n        .gte('date', weeklyData.week_start)\n        .lte('date', weeklyData.week_end)\n\n      // Get financial data\n      const { data: financeData } = await supabase\n        .from('finance_records')\n        .select('total_revenue, net_profit')\n        .eq('user_id', user.id)\n        .gte('date', weeklyData.week_start)\n        .lte('date', weeklyData.week_end)\n\n      const worshipAverage = worshipData?.length > 0 \n        ? worshipData.reduce((sum, item) => sum + item.worship_percentage, 0) / worshipData.length \n        : 0\n\n      const totalLearningHours = learningData?.reduce((sum, item) => sum + item.total_hours, 0) || 0\n      const totalProjectHours = projectData?.reduce((sum, item) => sum + item.total_hours, 0) || 0\n      const totalTemplateHours = templateData?.reduce((sum, item) => sum + item.total_hours, 0) || 0\n      const totalAppHours = appData?.reduce((sum, item) => sum + item.total_hours, 0) || 0\n      const totalRevenue = financeData?.reduce((sum, item) => sum + item.total_revenue, 0) || 0\n      const totalProfit = financeData?.reduce((sum, item) => sum + item.net_profit, 0) || 0\n\n      setWeeklyData(prev => ({\n        ...prev,\n        worship_average: Math.round(worshipAverage),\n        learning_hours: Math.round(totalLearningHours * 10) / 10,\n        project_hours: Math.round(totalProjectHours * 10) / 10,\n        template_hours: Math.round(totalTemplateHours * 10) / 10,\n        app_hours: Math.round(totalAppHours * 10) / 10,\n        total_revenue: Math.round(totalRevenue * 100) / 100,\n        net_profit: Math.round(totalProfit * 100) / 100\n      }))\n    } catch (error) {\n      console.error('Error calculating weekly stats:', error)\n    }\n  }\n\n  const handleSave = async () => {\n    setSaving(true)\n    try {\n      const dataToSave = {\n        user_id: user.id,\n        week_start: weeklyData.week_start,\n        week_end: weeklyData.week_end,\n        worship_average: weeklyData.worship_average,\n        learning_hours: weeklyData.learning_hours,\n        project_hours: weeklyData.project_hours,\n        template_hours: weeklyData.template_hours,\n        app_hours: weeklyData.app_hours,\n        total_revenue: weeklyData.total_revenue,\n        net_profit: weeklyData.net_profit,\n        goals_achieved: weeklyData.goals_achieved,\n        challenges_faced: weeklyData.challenges_faced,\n        lessons_learned: weeklyData.lessons_learned,\n        next_week_goals: weeklyData.next_week_goals,\n        overall_rating: weeklyData.overall_rating,\n        weekly_notes: weeklyData.weekly_notes\n      }\n\n      if (weeklyData.id) {\n        await supabase\n          .from('weekly_records')\n          .update(dataToSave)\n          .eq('id', weeklyData.id)\n      } else {\n        const { data } = await supabase\n          .from('weekly_records')\n          .insert(dataToSave)\n          .select()\n          .single()\n        \n        if (data) {\n          setWeeklyData(prev => ({ ...prev, id: data.id }))\n        }\n      }\n\n      alert('تم حفظ البيانات بنجاح!')\n    } catch (error) {\n      console.error('Error saving weekly data:', error)\n      alert('حدث خطأ أثناء حفظ البيانات')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleFieldChange = (field: keyof WeeklyData, value: any) => {\n    setWeeklyData(prev => ({ ...prev, [field]: value }))\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  const getRatingColor = (rating: number) => {\n    if (rating >= 8) return 'text-green-600'\n    if (rating >= 6) return 'text-yellow-600'\n    return 'text-red-600'\n  }\n\n  const getRatingEmoji = (rating: number) => {\n    if (rating >= 9) return '🌟'\n    if (rating >= 8) return '😊'\n    if (rating >= 6) return '😐'\n    if (rating >= 4) return '😕'\n    return '😞'\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-blue-600 hover:text-blue-800 ml-4\">\n                ← العودة للوحة الرئيسية\n              </Link>\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                📊 المتابعة الأسبوعية\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <div className=\"text-sm text-gray-600\">\n                {weeklyData.week_start} إلى {weeklyData.week_end}\n              </div>\n              <div className={`px-3 py-1 rounded-full text-sm font-medium ${\n                weeklyData.overall_rating >= 8 ? 'bg-green-100 text-green-800' :\n                weeklyData.overall_rating >= 6 ? 'bg-yellow-100 text-yellow-800' :\n                'bg-red-100 text-red-800'\n              }`}>\n                {getRatingEmoji(weeklyData.overall_rating)} {weeklyData.overall_rating}/10\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6\">\n            {/* Week Selector */}\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  بداية الأسبوع\n                </label>\n                <input\n                  type=\"date\"\n                  value={weeklyData.week_start}\n                  onChange={(e) => handleFieldChange('week_start', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  نهاية الأسبوع\n                </label>\n                <input\n                  type=\"date\"\n                  value={weeklyData.week_end}\n                  onChange={(e) => handleFieldChange('week_end', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                />\n              </div>\n            </div>\n\n            {/* Auto-calculated Stats */}\n            <div className=\"mb-8\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">الإحصائيات الأسبوعية</h3>\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"bg-blue-50 p-4 rounded-lg text-center\">\n                  <p className=\"text-sm text-blue-600 font-medium\">متوسط العبادات</p>\n                  <p className=\"text-2xl font-bold text-blue-800\">{weeklyData.worship_average}%</p>\n                </div>\n                <div className=\"bg-green-50 p-4 rounded-lg text-center\">\n                  <p className=\"text-sm text-green-600 font-medium\">ساعات التعلم</p>\n                  <p className=\"text-2xl font-bold text-green-800\">{weeklyData.learning_hours}</p>\n                </div>\n                <div className=\"bg-purple-50 p-4 rounded-lg text-center\">\n                  <p className=\"text-sm text-purple-600 font-medium\">ساعات المشاريع</p>\n                  <p className=\"text-2xl font-bold text-purple-800\">{weeklyData.project_hours}</p>\n                </div>\n                <div className=\"bg-yellow-50 p-4 rounded-lg text-center\">\n                  <p className=\"text-sm text-yellow-600 font-medium\">صافي الربح</p>\n                  <p className=\"text-2xl font-bold text-yellow-800\">${weeklyData.net_profit}</p>\n                </div>\n              </div>\n              <button\n                onClick={calculateWeeklyStats}\n                className=\"mt-4 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700\"\n              >\n                إعادة حساب الإحصائيات\n              </button>\n            </div>\n\n            {/* Weekly Review */}\n            <div className=\"space-y-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  الأهداف المحققة هذا الأسبوع\n                </label>\n                <textarea\n                  value={weeklyData.goals_achieved}\n                  onChange={(e) => handleFieldChange('goals_achieved', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"اكتب الأهداف التي حققتها...\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  التحديات التي واجهتها\n                </label>\n                <textarea\n                  value={weeklyData.challenges_faced}\n                  onChange={(e) => handleFieldChange('challenges_faced', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"اكتب التحديات والصعوبات...\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  الدروس المستفادة\n                </label>\n                <textarea\n                  value={weeklyData.lessons_learned}\n                  onChange={(e) => handleFieldChange('lessons_learned', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"اكتب الدروس والخبرات المكتسبة...\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  أهداف الأسبوع القادم\n                </label>\n                <textarea\n                  value={weeklyData.next_week_goals}\n                  onChange={(e) => handleFieldChange('next_week_goals', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"اكتب أهدافك للأسبوع القادم...\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  التقييم العام للأسبوع (1-10)\n                </label>\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <input\n                    type=\"range\"\n                    min=\"1\"\n                    max=\"10\"\n                    value={weeklyData.overall_rating}\n                    onChange={(e) => handleFieldChange('overall_rating', parseInt(e.target.value))}\n                    className=\"flex-1\"\n                  />\n                  <span className={`text-2xl font-bold ${getRatingColor(weeklyData.overall_rating)}`}>\n                    {getRatingEmoji(weeklyData.overall_rating)} {weeklyData.overall_rating}\n                  </span>\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  ملاحظات إضافية\n                </label>\n                <textarea\n                  value={weeklyData.weekly_notes}\n                  onChange={(e) => handleFieldChange('weekly_notes', e.target.value)}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"اكتب أي ملاحظات إضافية...\"\n                />\n              </div>\n            </div>\n\n            <div className=\"mt-8 pt-6 border-t\">\n              <button\n                onClick={handleSave}\n                disabled={saving}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n              >\n                {saving ? 'جاري الحفظ...' : 'حفظ التقييم الأسبوعي'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AA0Be,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,YAAY;QACZ,UAAU;QACV,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;QACf,gBAAgB;QAChB,WAAW;QACX,eAAe;QACf,YAAY;QACZ,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,cAAc;IAChB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,SAAS;QACP,MAAM,QAAQ,IAAI;QAClB,MAAM,MAAM,MAAM,MAAM;QACxB,MAAM,OAAO,MAAM,OAAO,KAAK,MAAM,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,EAAE,4BAA4B;;QACtF,MAAM,SAAS,IAAI,KAAK,MAAM,OAAO,CAAC;QACtC,OAAO,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC3C;IAEA,SAAS;QACP,MAAM,QAAQ,IAAI;QAClB,MAAM,MAAM,MAAM,MAAM;QACxB,MAAM,OAAO,MAAM,OAAO,KAAK,MAAM,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,4BAA4B;;QACrF,MAAM,SAAS,IAAI,KAAK,MAAM,OAAO,CAAC;QACtC,OAAO,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC3C;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM,WAAW,UAAU;KAAC;IAEhC,MAAM,YAAY;QAChB,MAAM,YAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;QACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,UAAS,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;QACd,OAAO;YACL,QAAQ;QACV;QACA,WAAW;IACb;IAEA,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,EAAE,CAAC,cAAc,WAAW,UAAU,EACtC,MAAM;YAET,IAAI,MAAM;gBACR,cAAc;oBACZ,IAAI,KAAK,EAAE;oBACX,YAAY,KAAK,UAAU;oBAC3B,UAAU,KAAK,QAAQ;oBACvB,iBAAiB,KAAK,eAAe,IAAI;oBACzC,gBAAgB,KAAK,cAAc,IAAI;oBACvC,eAAe,KAAK,aAAa,IAAI;oBACrC,gBAAgB,KAAK,cAAc,IAAI;oBACvC,WAAW,KAAK,SAAS,IAAI;oBAC7B,eAAe,KAAK,aAAa,IAAI;oBACrC,YAAY,KAAK,UAAU,IAAI;oBAC/B,gBAAgB,KAAK,cAAc,IAAI;oBACvC,kBAAkB,KAAK,gBAAgB,IAAI;oBAC3C,iBAAiB,KAAK,eAAe,IAAI;oBACzC,iBAAiB,KAAK,eAAe,IAAI;oBACzC,gBAAgB,KAAK,cAAc,IAAI;oBACvC,cAAc,KAAK,YAAY,IAAI;gBACrC;YACF,OAAO;gBACL,iDAAiD;gBACjD,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI;YACF,sBAAsB;YACtB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,sBACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,WAAW,UAAU,EACjC,GAAG,CAAC,QAAQ,WAAW,QAAQ;YAElC,qBAAqB;YACrB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,oBACL,MAAM,CAAC,eACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,WAAW,UAAU,EACjC,GAAG,CAAC,QAAQ,WAAW,QAAQ;YAElC,oBAAoB;YACpB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,eACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,WAAW,UAAU,EACjC,GAAG,CAAC,QAAQ,WAAW,QAAQ;YAElC,qBAAqB;YACrB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,oBACL,MAAM,CAAC,eACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,WAAW,UAAU,EACjC,GAAG,CAAC,QAAQ,WAAW,QAAQ;YAElC,gBAAgB;YAChB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,eACL,MAAM,CAAC,eACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,WAAW,UAAU,EACjC,GAAG,CAAC,QAAQ,WAAW,QAAQ;YAElC,qBAAqB;YACrB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,6BACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,WAAW,UAAU,EACjC,GAAG,CAAC,QAAQ,WAAW,QAAQ;YAElC,MAAM,iBAAiB,aAAa,SAAS,IACzC,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,kBAAkB,EAAE,KAAK,YAAY,MAAM,GACxF;YAEJ,MAAM,qBAAqB,cAAc,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE,MAAM;YAC7F,MAAM,oBAAoB,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE,MAAM;YAC3F,MAAM,qBAAqB,cAAc,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE,MAAM;YAC7F,MAAM,gBAAgB,SAAS,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE,MAAM;YACnF,MAAM,eAAe,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,aAAa,EAAE,MAAM;YACxF,MAAM,cAAc,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,MAAM;YAEpF,cAAc,CAAA,OAAQ,CAAC;oBACrB,GAAG,IAAI;oBACP,iBAAiB,KAAK,KAAK,CAAC;oBAC5B,gBAAgB,KAAK,KAAK,CAAC,qBAAqB,MAAM;oBACtD,eAAe,KAAK,KAAK,CAAC,oBAAoB,MAAM;oBACpD,gBAAgB,KAAK,KAAK,CAAC,qBAAqB,MAAM;oBACtD,WAAW,KAAK,KAAK,CAAC,gBAAgB,MAAM;oBAC5C,eAAe,KAAK,KAAK,CAAC,eAAe,OAAO;oBAChD,YAAY,KAAK,KAAK,CAAC,cAAc,OAAO;gBAC9C,CAAC;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,aAAa;QACjB,UAAU;QACV,IAAI;YACF,MAAM,aAAa;gBACjB,SAAS,KAAK,EAAE;gBAChB,YAAY,WAAW,UAAU;gBACjC,UAAU,WAAW,QAAQ;gBAC7B,iBAAiB,WAAW,eAAe;gBAC3C,gBAAgB,WAAW,cAAc;gBACzC,eAAe,WAAW,aAAa;gBACvC,gBAAgB,WAAW,cAAc;gBACzC,WAAW,WAAW,SAAS;gBAC/B,eAAe,WAAW,aAAa;gBACvC,YAAY,WAAW,UAAU;gBACjC,gBAAgB,WAAW,cAAc;gBACzC,kBAAkB,WAAW,gBAAgB;gBAC7C,iBAAiB,WAAW,eAAe;gBAC3C,iBAAiB,WAAW,eAAe;gBAC3C,gBAAgB,WAAW,cAAc;gBACzC,cAAc,WAAW,YAAY;YACvC;YAEA,IAAI,WAAW,EAAE,EAAE;gBACjB,MAAM,SACH,IAAI,CAAC,kBACL,MAAM,CAAC,YACP,EAAE,CAAC,MAAM,WAAW,EAAE;YAC3B,OAAO;gBACL,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,SACpB,IAAI,CAAC,kBACL,MAAM,CAAC,YACP,MAAM,GACN,MAAM;gBAET,IAAI,MAAM;oBACR,cAAc,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,IAAI,KAAK,EAAE;wBAAC,CAAC;gBACjD;YACF;YAEA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAyB;QAClD,cAAc,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACpD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,GAAG,OAAO;QACxB,IAAI,UAAU,GAAG,OAAO;QACxB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAyC;;;;;;kDAG3E,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;4CACZ,WAAW,UAAU;4CAAC;4CAAM,WAAW,QAAQ;;;;;;;kDAElD,8OAAC;wCAAI,WAAW,CAAC,2CAA2C,EAC1D,WAAW,cAAc,IAAI,IAAI,gCACjC,WAAW,cAAc,IAAI,IAAI,kCACjC,2BACA;;4CACC,eAAe,WAAW,cAAc;4CAAE;4CAAE,WAAW,cAAc;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQjF,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,WAAW,UAAU;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;gDAC/D,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,MAAK;gDACL,OAAO,WAAW,QAAQ;gDAC1B,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC7D,WAAU;;;;;;;;;;;;;;;;;;0CAMhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;;4DAAoC,WAAW,eAAe;4DAAC;;;;;;;;;;;;;0DAE9E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEAAqC,WAAW,cAAc;;;;;;;;;;;;0DAE7E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAsC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAsC,WAAW,aAAa;;;;;;;;;;;;0DAE7E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAsC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;;4DAAqC;4DAAE,WAAW,UAAU;;;;;;;;;;;;;;;;;;;kDAG7E,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;0CAMH,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,WAAW,cAAc;gDAChC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;gDACnE,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,WAAW,gBAAgB;gDAClC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACrE,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,WAAW,eAAe;gDACjC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDACpE,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,WAAW,eAAe;gDACjC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDACpE,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,WAAW,cAAc;wDAChC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wDAC5E,WAAU;;;;;;kEAEZ,8OAAC;wDAAK,WAAW,CAAC,mBAAmB,EAAE,eAAe,WAAW,cAAc,GAAG;;4DAC/E,eAAe,WAAW,cAAc;4DAAE;4DAAE,WAAW,cAAc;;;;;;;;;;;;;;;;;;;kDAK5E,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,WAAW,YAAY;gDAC9B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDACjE,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}]}