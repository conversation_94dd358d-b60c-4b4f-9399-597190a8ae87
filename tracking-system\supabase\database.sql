-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- Create users table extension
CREATE TABLE IF NOT EXISTS public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on users table
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;

-- Create policy for users table
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Create worship_records table
CREATE TABLE IF NOT EXISTS public.worship_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  sleep_on_time BOOLEAN DEFAULT FALSE,
  wake_up_on_time BOOLEAN DEFAULT FALSE,
  night_prayer BOOLEAN DEFAULT FALSE,
  fajr_prayer BOOLEAN DEFAULT FALSE,
  quran_reading BOOLEAN DEFAULT FALSE,
  istighfar_200 BOOLEAN DEFAULT FALSE,
  salawat_200 BOOLEAN DEFAULT FALSE,
  tasbih_100 BOOLEAN DEFAULT FALSE,
  la_ilaha_illa_allah_100 BOOLEAN DEFAULT FALSE,
  la_ilaha_illa_anta_50 BOOLEAN DEFAULT FALSE,
  morning_exercise BOOLEAN DEFAULT FALSE,
  shower BOOLEAN DEFAULT FALSE,
  duha_prayer BOOLEAN DEFAULT FALSE,
  dhuhr_prayer BOOLEAN DEFAULT FALSE,
  asr_prayer BOOLEAN DEFAULT FALSE,
  maghrib_prayer BOOLEAN DEFAULT FALSE,
  isha_prayer BOOLEAN DEFAULT FALSE,
  slept_after_fajr BOOLEAN DEFAULT FALSE,
  worship_percentage DECIMAL(5,2) DEFAULT 0,
  spiritual_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Enable RLS on worship_records table
ALTER TABLE public.worship_records ENABLE ROW LEVEL SECURITY;

-- Create policies for worship_records table
CREATE POLICY "Users can view own worship records" ON public.worship_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own worship records" ON public.worship_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own worship records" ON public.worship_records
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own worship records" ON public.worship_records
  FOR DELETE USING (auth.uid() = user_id);

-- Create learning_records table
CREATE TABLE IF NOT EXISTS public.learning_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  laravel_time INTEGER DEFAULT 0,
  laravel_topic TEXT,
  laravel_progress DECIMAL(5,2) DEFAULT 0,
  laravel_rating INTEGER CHECK (laravel_rating >= 1 AND laravel_rating <= 10),
  nodejs_time INTEGER DEFAULT 0,
  nodejs_topic TEXT,
  nodejs_progress DECIMAL(5,2) DEFAULT 0,
  nextjs_time INTEGER DEFAULT 0,
  nextjs_topic TEXT,
  nextjs_progress DECIMAL(5,2) DEFAULT 0,
  mean_rating INTEGER CHECK (mean_rating >= 1 AND mean_rating <= 10),
  flutter_time INTEGER DEFAULT 0,
  flutter_topic TEXT,
  flutter_progress DECIMAL(5,2) DEFAULT 0,
  database_time INTEGER DEFAULT 0,
  database_type TEXT,
  database_progress DECIMAL(5,2) DEFAULT 0,
  total_hours DECIMAL(4,2) DEFAULT 0,
  key_learnings TEXT,
  challenges TEXT,
  tomorrow_plan TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Enable RLS on learning_records table
ALTER TABLE public.learning_records ENABLE ROW LEVEL SECURITY;

-- Create policies for learning_records table
CREATE POLICY "Users can view own learning records" ON public.learning_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own learning records" ON public.learning_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own learning records" ON public.learning_records
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own learning records" ON public.learning_records
  FOR DELETE USING (auth.uid() = user_id);

-- Create template_records table
CREATE TABLE IF NOT EXISTS public.template_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  shopify_time INTEGER DEFAULT 0,
  shopify_features TEXT,
  shopify_progress DECIMAL(5,2) DEFAULT 0,
  shopify_issues TEXT,
  shopify_solutions TEXT,
  salla_progress DECIMAL(5,2) DEFAULT 0,
  salla_notes TEXT,
  wordpress_progress DECIMAL(5,2) DEFAULT 0,
  wordpress_notes TEXT,
  platform_api TEXT,
  platform_functions TEXT,
  platform_testing BOOLEAN DEFAULT FALSE,
  wholesale_prices TEXT,
  expected_revenue DECIMAL(10,2) DEFAULT 0,
  tomorrow_plan TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Enable RLS on template_records table
ALTER TABLE public.template_records ENABLE ROW LEVEL SECURITY;

-- Create policies for template_records table
CREATE POLICY "Users can view own template records" ON public.template_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own template records" ON public.template_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own template records" ON public.template_records
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own template records" ON public.template_records
  FOR DELETE USING (auth.uid() = user_id);

-- Create app_records table
CREATE TABLE IF NOT EXISTS public.app_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  shopify_functions TEXT,
  shopify_integration TEXT,
  shopify_api_test TEXT,
  shopify_status TEXT,
  salla_functions TEXT,
  salla_integration TEXT,
  salla_api_test TEXT,
  salla_status TEXT,
  wordpress_functions TEXT,
  wordpress_integration TEXT,
  wordpress_api_test TEXT,
  wordpress_status TEXT,
  future_app_ideas TEXT,
  future_app_planning TEXT,
  future_app_features TEXT,
  integration_issues TEXT,
  price_updates TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Enable RLS on app_records table
ALTER TABLE public.app_records ENABLE ROW LEVEL SECURITY;

-- Create policies for app_records table
CREATE POLICY "Users can view own app records" ON public.app_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own app records" ON public.app_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own app records" ON public.app_records
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own app records" ON public.app_records
  FOR DELETE USING (auth.uid() = user_id);

-- Create project_records table
CREATE TABLE IF NOT EXISTS public.project_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  period1_project TEXT,
  period1_tasks TEXT,
  period1_time INTEGER DEFAULT 0,
  period2_project TEXT,
  period2_tasks TEXT,
  period2_time INTEGER DEFAULT 0,
  dashsy_tasks TEXT,
  dashsy_progress DECIMAL(5,2) DEFAULT 0,
  dashsy_issues TEXT,
  dashsy_solutions TEXT,
  dashsy_tomorrow TEXT,
  syriana_tasks TEXT,
  syriana_progress DECIMAL(5,2) DEFAULT 0,
  syriana_issues TEXT,
  syriana_solutions TEXT,
  syriana_tomorrow TEXT,
  booking_tasks TEXT,
  booking_progress DECIMAL(5,2) DEFAULT 0,
  booking_issues TEXT,
  booking_solutions TEXT,
  booking_tomorrow TEXT,
  total_hours DECIMAL(4,2) DEFAULT 0,
  productivity_rating INTEGER CHECK (productivity_rating >= 1 AND productivity_rating <= 10),
  tomorrow_plan TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Enable RLS on project_records table
ALTER TABLE public.project_records ENABLE ROW LEVEL SECURITY;

-- Create policies for project_records table
CREATE POLICY "Users can view own project records" ON public.project_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own project records" ON public.project_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own project records" ON public.project_records
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own project records" ON public.project_records
  FOR DELETE USING (auth.uid() = user_id);

-- Create finance_records table
CREATE TABLE IF NOT EXISTS public.finance_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  shopify_revenue DECIMAL(10,2) DEFAULT 0,
  salla_revenue DECIMAL(10,2) DEFAULT 0,
  wordpress_revenue DECIMAL(10,2) DEFAULT 0,
  apps_revenue DECIMAL(10,2) DEFAULT 0,
  projects_revenue DECIMAL(10,2) DEFAULT 0,
  total_revenue DECIMAL(10,2) DEFAULT 0,
  daily_expenses DECIMAL(10,2) DEFAULT 0,
  net_profit DECIMAL(10,2) DEFAULT 0,
  monthly_cumulative DECIMAL(10,2) DEFAULT 0,
  financial_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Enable RLS on finance_records table
ALTER TABLE public.finance_records ENABLE ROW LEVEL SECURITY;

-- Create policies for finance_records table
CREATE POLICY "Users can view own finance records" ON public.finance_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own finance records" ON public.finance_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own finance records" ON public.finance_records
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own finance records" ON public.finance_records
  FOR DELETE USING (auth.uid() = user_id);

-- Create weekly_records table
CREATE TABLE IF NOT EXISTS public.weekly_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  week_start DATE NOT NULL,
  week_end DATE NOT NULL,
  routine_days INTEGER DEFAULT 0,
  worship_percentage DECIMAL(5,2) DEFAULT 0,
  total_learning_hours DECIMAL(5,2) DEFAULT 0,
  key_topics TEXT,
  laravel_progress DECIMAL(5,2) DEFAULT 0,
  mean_progress DECIMAL(5,2) DEFAULT 0,
  flutter_progress DECIMAL(5,2) DEFAULT 0,
  database_progress DECIMAL(5,2) DEFAULT 0,
  shopify_progress DECIMAL(5,2) DEFAULT 0,
  platform_progress DECIMAL(5,2) DEFAULT 0,
  apps_progress DECIMAL(5,2) DEFAULT 0,
  dashsy_progress DECIMAL(5,2) DEFAULT 0,
  syriana_progress DECIMAL(5,2) DEFAULT 0,
  booking_progress DECIMAL(5,2) DEFAULT 0,
  weekly_revenue DECIMAL(10,2) DEFAULT 0,
  main_challenges TEXT,
  main_solutions TEXT,
  next_week_plan TEXT,
  overall_rating INTEGER CHECK (overall_rating >= 1 AND overall_rating <= 10),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, week_start)
);

-- Enable RLS on weekly_records table
ALTER TABLE public.weekly_records ENABLE ROW LEVEL SECURITY;

-- Create policies for weekly_records table
CREATE POLICY "Users can view own weekly records" ON public.weekly_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own weekly records" ON public.weekly_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own weekly records" ON public.weekly_records
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own weekly records" ON public.weekly_records
  FOR DELETE USING (auth.uid() = user_id);

-- Create dashboard_records table
CREATE TABLE IF NOT EXISTS public.dashboard_records (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
  date DATE NOT NULL,
  routine_status TEXT CHECK (routine_status IN ('green', 'yellow', 'red')) DEFAULT 'yellow',
  learning_progress DECIMAL(5,2) DEFAULT 0,
  templates_progress DECIMAL(5,2) DEFAULT 0,
  projects_progress DECIMAL(5,2) DEFAULT 0,
  daily_revenue DECIMAL(10,2) DEFAULT 0,
  monthly_routine_percentage DECIMAL(5,2) DEFAULT 0,
  monthly_learning_hours DECIMAL(5,2) DEFAULT 0,
  monthly_templates_progress DECIMAL(5,2) DEFAULT 0,
  monthly_projects_progress DECIMAL(5,2) DEFAULT 0,
  urgent_alerts TEXT,
  quick_notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, date)
);

-- Enable RLS on dashboard_records table
ALTER TABLE public.dashboard_records ENABLE ROW LEVEL SECURITY;

-- Create policies for dashboard_records table
CREATE POLICY "Users can view own dashboard records" ON public.dashboard_records
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own dashboard records" ON public.dashboard_records
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own dashboard records" ON public.dashboard_records
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own dashboard records" ON public.dashboard_records
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, full_name, avatar_url)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name', NEW.raw_user_meta_data->>'avatar_url');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_worship_records_updated_at BEFORE UPDATE ON public.worship_records
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_learning_records_updated_at BEFORE UPDATE ON public.learning_records
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_template_records_updated_at BEFORE UPDATE ON public.template_records
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_app_records_updated_at BEFORE UPDATE ON public.app_records
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_project_records_updated_at BEFORE UPDATE ON public.project_records
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_finance_records_updated_at BEFORE UPDATE ON public.finance_records
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_weekly_records_updated_at BEFORE UPDATE ON public.weekly_records
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_dashboard_records_updated_at BEFORE UPDATE ON public.dashboard_records
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
