'use client'

import { useEffect, useState } from 'react'
import { getSupabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface AppData {
  id?: string
  date: string
  shopify_app_time: number
  salla_app_time: number
  wordpress_plugin_time: number
  mobile_app_time: number
  shopify_app_progress: number
  salla_app_progress: number
  wordpress_plugin_progress: number
  mobile_app_progress: number
  shopify_apps: string
  salla_apps: string
  wordpress_plugins: string
  mobile_apps: string
  app_notes: string
}

export default function AppsPage() {
  const [user, setUser] = useState<any>(null)
  const [appData, setAppData] = useState<AppData>({
    date: new Date().toISOString().split('T')[0],
    shopify_app_time: 0,
    salla_app_time: 0,
    wordpress_plugin_time: 0,
    mobile_app_time: 0,
    shopify_app_progress: 0,
    salla_app_progress: 0,
    wordpress_plugin_progress: 0,
    mobile_app_progress: 0,
    shopify_apps: '',
    salla_apps: '',
    wordpress_plugins: '',
    mobile_apps: '',
    app_notes: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadAppData()
    }
  }, [user, appData.date])

  const checkUser = async () => {
    const supabase = await getSupabase()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
    } else {
      setUser(user)
    }
    setLoading(false)
  }

  const loadAppData = async () => {
    try {
      // Load from localStorage
      const storageKey = `apps_${user.id}_${appData.date}`
      const stored = localStorage.getItem(storageKey)

      if (stored) {
        const data = JSON.parse(stored)
        setAppData({
          id: data.id,
          date: data.date,
          shopify_app_time: data.shopify_app_time || 0,
          salla_app_time: data.salla_app_time || 0,
          wordpress_plugin_time: data.wordpress_plugin_time || 0,
          mobile_app_time: data.mobile_app_time || 0,
          shopify_app_progress: data.shopify_app_progress || 0,
          salla_app_progress: data.salla_app_progress || 0,
          wordpress_plugin_progress: data.wordpress_plugin_progress || 0,
          mobile_app_progress: data.mobile_app_progress || 0,
          shopify_apps: data.shopify_apps || '',
          salla_apps: data.salla_apps || '',
          wordpress_plugins: data.wordpress_plugins || '',
          mobile_apps: data.mobile_apps || '',
          app_notes: data.app_notes || ''
        })
      }
    } catch (error) {
      console.error('Error loading app data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateTotalHours = () => {
    return (
      appData.shopify_app_time +
      appData.salla_app_time +
      appData.wordpress_plugin_time +
      appData.mobile_app_time
    ) / 60 // Convert minutes to hours
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const totalHours = calculateTotalHours()
      const dataToSave = {
        user_id: user.id,
        date: appData.date,
        shopify_app_time: appData.shopify_app_time,
        salla_app_time: appData.salla_app_time,
        wordpress_plugin_time: appData.wordpress_plugin_time,
        mobile_app_time: appData.mobile_app_time,
        shopify_app_progress: appData.shopify_app_progress,
        salla_app_progress: appData.salla_app_progress,
        wordpress_plugin_progress: appData.wordpress_plugin_progress,
        mobile_app_progress: appData.mobile_app_progress,
        shopify_apps: appData.shopify_apps,
        salla_apps: appData.salla_apps,
        wordpress_plugins: appData.wordpress_plugins,
        mobile_apps: appData.mobile_apps,
        total_hours: totalHours,
        app_notes: appData.app_notes
      }

      // Add ID and timestamp
      const finalData = {
        ...dataToSave,
        id: appData.id || `apps_${Date.now()}`,
        created_at: new Date().toISOString()
      }

      // Save to localStorage
      const storageKey = `apps_${user.id}_${appData.date}`
      localStorage.setItem(storageKey, JSON.stringify(finalData))

      // Update state with new ID if it was a new record
      if (!appData.id) {
        setAppData(prev => ({ ...prev, id: finalData.id }))
      }

      alert('تم حفظ البيانات بنجاح!')
    } catch (error) {
      console.error('Error saving app data:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handleTimeChange = (field: keyof AppData, value: number) => {
    setAppData(prev => ({ ...prev, [field]: value }))
  }

  const handleProgressChange = (field: keyof AppData, value: number) => {
    setAppData(prev => ({ ...prev, [field]: value }))
  }

  const handleAppsChange = (field: keyof AppData, value: string) => {
    setAppData(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  const totalHours = calculateTotalHours()

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للوحة الرئيسية
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                📱 تطبيقات المنصات
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                إجمالي: {totalHours.toFixed(1)} ساعة
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Date Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التاريخ
              </label>
              <input
                type="date"
                value={appData.date}
                onChange={(e) => setAppData(prev => ({ ...prev, date: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* App Development */}
            <div className="space-y-8">
              {/* Shopify Apps */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🛍️</span>
                  Shopify Apps
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={appData.shopify_app_time}
                      onChange={(e) => handleTimeChange('shopify_app_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={appData.shopify_app_progress}
                      onChange={(e) => handleProgressChange('shopify_app_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      التطبيقات المطورة
                    </label>
                    <textarea
                      value={appData.shopify_apps}
                      onChange={(e) => handleAppsChange('shopify_apps', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب أسماء التطبيقات..."
                    />
                  </div>
                </div>
              </div>

              {/* Salla Apps */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🏪</span>
                  Salla Apps
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={appData.salla_app_time}
                      onChange={(e) => handleTimeChange('salla_app_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={appData.salla_app_progress}
                      onChange={(e) => handleProgressChange('salla_app_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      التطبيقات المطورة
                    </label>
                    <textarea
                      value={appData.salla_apps}
                      onChange={(e) => handleAppsChange('salla_apps', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب أسماء التطبيقات..."
                    />
                  </div>
                </div>
              </div>

              {/* WordPress Plugins */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🔌</span>
                  WordPress Plugins
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={appData.wordpress_plugin_time}
                      onChange={(e) => handleTimeChange('wordpress_plugin_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={appData.wordpress_plugin_progress}
                      onChange={(e) => handleProgressChange('wordpress_plugin_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الإضافات المطورة
                    </label>
                    <textarea
                      value={appData.wordpress_plugins}
                      onChange={(e) => handleAppsChange('wordpress_plugins', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب أسماء الإضافات..."
                    />
                  </div>
                </div>
              </div>

              {/* Mobile Apps */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">📱</span>
                  Mobile Apps (Flutter)
                </h3>
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={appData.mobile_app_time}
                      onChange={(e) => handleTimeChange('mobile_app_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={appData.mobile_app_progress}
                      onChange={(e) => handleProgressChange('mobile_app_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      التطبيقات المطورة
                    </label>
                    <textarea
                      value={appData.mobile_apps}
                      onChange={(e) => handleAppsChange('mobile_apps', e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="اكتب أسماء التطبيقات..."
                    />
                  </div>
                </div>
              </div>

              {/* App Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات التطبيقات
                </label>
                <textarea
                  value={appData.app_notes}
                  onChange={(e) => setAppData(prev => ({ ...prev, app_notes: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب ملاحظاتك حول تطوير التطبيقات..."
                />
              </div>
            </div>

            <div className="mt-8 pt-6 border-t">
              <button
                onClick={handleSave}
                disabled={saving}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ البيانات'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
