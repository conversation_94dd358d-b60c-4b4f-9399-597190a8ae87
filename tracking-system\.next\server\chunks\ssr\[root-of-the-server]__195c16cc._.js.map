{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/lib/supabase.ts"], "sourcesContent": ["// Temporary authentication system using localStorage\ninterface User {\n  id: string\n  email: string\n  full_name?: string\n  created_at: string\n}\n\ninterface AuthResponse {\n  data: { user: User | null }\n  error: any\n}\n\n// Mock database using localStorage\nconst getStoredUsers = (): User[] => {\n  if (typeof window === 'undefined') return []\n  const users = localStorage.getItem('tracking_users')\n  return users ? JSON.parse(users) : []\n}\n\nconst saveUsers = (users: User[]) => {\n  if (typeof window === 'undefined') return\n  localStorage.setItem('tracking_users', JSON.stringify(users))\n}\n\nconst getCurrentUser = (): User | null => {\n  if (typeof window === 'undefined') return null\n  const user = localStorage.getItem('tracking_current_user')\n  return user ? JSON.parse(user) : null\n}\n\nconst setCurrentUser = (user: User | null) => {\n  if (typeof window === 'undefined') return\n  if (user) {\n    localStorage.setItem('tracking_current_user', JSON.stringify(user))\n  } else {\n    localStorage.removeItem('tracking_current_user')\n  }\n}\n\n// Initialize demo users if none exist\nconst initializeDemoUsers = () => {\n  if (typeof window === 'undefined') return\n  const existingUsers = getStoredUsers()\n  if (existingUsers.length === 0) {\n    const demoUsers = [\n      {\n        id: 'demo-user-1',\n        email: '<EMAIL>',\n        full_name: 'مستخدم تجريبي',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'user-test-1',\n        email: '<EMAIL>',\n        full_name: 'سمير الكحلان',\n        created_at: new Date().toISOString()\n      }\n    ]\n    saveUsers(demoUsers)\n  }\n}\n\n// Mock Supabase client\nconst mockSupabase = {\n  auth: {\n    getUser: async (): Promise<AuthResponse> => {\n      const user = getCurrentUser()\n      return { data: { user }, error: null }\n    },\n\n    signOut: async () => {\n      setCurrentUser(null)\n      return { error: null }\n    },\n\n    signInWithPassword: async ({ email, password }: { email: string, password: string }) => {\n      const users = getStoredUsers()\n      const user = users.find(u => u.email === email)\n\n      if (!user) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني غير مسجل' }\n        }\n      }\n\n      // For demo, accept any password\n      setCurrentUser(user)\n      return { data: { user }, error: null }\n    },\n\n    signUp: async ({ email, password, options }: {\n      email: string,\n      password: string,\n      options?: { data?: { full_name?: string } }\n    }) => {\n      const users = getStoredUsers()\n      const existingUser = users.find(u => u.email === email)\n\n      if (existingUser) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني مسجل بالفعل' }\n        }\n      }\n\n      const newUser: User = {\n        id: Date.now().toString(),\n        email,\n        full_name: options?.data?.full_name,\n        created_at: new Date().toISOString()\n      }\n\n      users.push(newUser)\n      saveUsers(users)\n      setCurrentUser(newUser)\n\n      return { data: { user: newUser }, error: null }\n    }\n  },\n\n  from: (table: string) => ({\n    select: (columns?: string) => ({\n      eq: (column: string, value: any) => ({\n        single: () => Promise.resolve({ data: null, error: null })\n      }),\n      order: (column: string) => Promise.resolve({ data: [], error: null })\n    }),\n    insert: (data: any) => Promise.resolve({ data: null, error: null }),\n    update: (data: any) => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    delete: () => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    upsert: (data: any) => ({\n      select: () => Promise.resolve({ data: null, error: null })\n    })\n  })\n}\n\n// For client-side operations\nexport const getSupabase = async () => {\n  initializeDemoUsers()\n  return mockSupabase\n}\n\n// For server-side operations\nexport const createServerClient = async () => {\n  return mockSupabase\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WorshipRecord {\n  id: string\n  user_id: string\n  date: string\n  sleep_on_time: boolean\n  wake_up_on_time: boolean\n  night_prayer: boolean\n  fajr_prayer: boolean\n  quran_reading: boolean\n  istighfar_200: boolean\n  salawat_200: boolean\n  tasbih_100: boolean\n  la_ilaha_illa_allah_100: boolean\n  la_ilaha_illa_anta_50: boolean\n  morning_exercise: boolean\n  shower: boolean\n  duha_prayer: boolean\n  dhuhr_prayer: boolean\n  asr_prayer: boolean\n  maghrib_prayer: boolean\n  isha_prayer: boolean\n  slept_after_fajr: boolean\n  worship_percentage: number\n  spiritual_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LearningRecord {\n  id: string\n  user_id: string\n  date: string\n  laravel_time: number\n  laravel_topic?: string\n  laravel_progress: number\n  laravel_rating: number\n  nodejs_time: number\n  nodejs_topic?: string\n  nodejs_progress: number\n  nextjs_time: number\n  nextjs_topic?: string\n  nextjs_progress: number\n  mean_rating: number\n  flutter_time: number\n  flutter_topic?: string\n  flutter_progress: number\n  database_time: number\n  database_type?: string\n  database_progress: number\n  total_hours: number\n  key_learnings?: string\n  challenges?: string\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TemplateRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_time: number\n  shopify_features?: string\n  shopify_progress: number\n  shopify_issues?: string\n  shopify_solutions?: string\n  salla_progress: number\n  salla_notes?: string\n  wordpress_progress: number\n  wordpress_notes?: string\n  platform_api?: string\n  platform_functions?: string\n  platform_testing: boolean\n  wholesale_prices?: string\n  expected_revenue: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AppRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_functions?: string\n  shopify_integration: string\n  shopify_api_test: string\n  shopify_status: string\n  salla_functions?: string\n  salla_integration: string\n  salla_api_test: string\n  salla_status: string\n  wordpress_functions?: string\n  wordpress_integration: string\n  wordpress_api_test: string\n  wordpress_status: string\n  future_app_ideas?: string\n  future_app_planning?: string\n  future_app_features?: string\n  integration_issues?: string\n  price_updates?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ProjectRecord {\n  id: string\n  user_id: string\n  date: string\n  period1_project?: string\n  period1_tasks?: string\n  period1_time: number\n  period2_project?: string\n  period2_tasks?: string\n  period2_time: number\n  dashsy_tasks?: string\n  dashsy_progress: number\n  dashsy_issues?: string\n  dashsy_solutions?: string\n  dashsy_tomorrow?: string\n  syriana_tasks?: string\n  syriana_progress: number\n  syriana_issues?: string\n  syriana_solutions?: string\n  syriana_tomorrow?: string\n  booking_tasks?: string\n  booking_progress: number\n  booking_issues?: string\n  booking_solutions?: string\n  booking_tomorrow?: string\n  total_hours: number\n  productivity_rating: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FinanceRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_revenue: number\n  salla_revenue: number\n  wordpress_revenue: number\n  apps_revenue: number\n  projects_revenue: number\n  total_revenue: number\n  daily_expenses: number\n  net_profit: number\n  monthly_cumulative: number\n  financial_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WeeklyRecord {\n  id: string\n  user_id: string\n  week_start: string\n  week_end: string\n  routine_days: number\n  worship_percentage: number\n  total_learning_hours: number\n  key_topics?: string\n  laravel_progress: number\n  mean_progress: number\n  flutter_progress: number\n  database_progress: number\n  shopify_progress: number\n  platform_progress: number\n  apps_progress: number\n  dashsy_progress: number\n  syriana_progress: number\n  booking_progress: number\n  weekly_revenue: number\n  main_challenges?: string\n  main_solutions?: string\n  next_week_plan?: string\n  overall_rating: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface DashboardRecord {\n  id: string\n  user_id: string\n  date: string\n  routine_status: 'green' | 'yellow' | 'red'\n  learning_progress: number\n  templates_progress: number\n  projects_progress: number\n  daily_revenue: number\n  monthly_routine_percentage: number\n  monthly_learning_hours: number\n  monthly_templates_progress: number\n  monthly_projects_progress: number\n  urgent_alerts?: string\n  quick_notes?: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAarD,mCAAmC;AACnC,MAAM,iBAAiB;IACrB,wCAAmC,OAAO,EAAE;;IAC5C,MAAM;AAER;AAEA,MAAM,YAAY,CAAC;IACjB,wCAAmC;;AAErC;AAEA,MAAM,iBAAiB;IACrB,wCAAmC,OAAO;;IAC1C,MAAM;AAER;AAEA,MAAM,iBAAiB,CAAC;IACtB,wCAAmC;;AAMrC;AAEA,sCAAsC;AACtC,MAAM,sBAAsB;IAC1B,wCAAmC;;IACnC,MAAM;AAkBR;AAEA,uBAAuB;AACvB,MAAM,eAAe;IACnB,MAAM;QACJ,SAAS;YACP,MAAM,OAAO;YACb,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,SAAS;YACP,eAAe;YACf,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,oBAAoB,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACjF,MAAM,QAAQ;YACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEzC,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAA6B;gBACjD;YACF;YAEA,gCAAgC;YAChC,eAAe;YACf,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,QAAQ,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAIxC;YACC,MAAM,QAAQ;YACd,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEjD,IAAI,cAAc;gBAChB,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAAgC;gBACpD;YACF;YAEA,MAAM,UAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB;gBACA,WAAW,SAAS,MAAM;gBAC1B,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,IAAI,CAAC;YACX,UAAU;YACV,eAAe;YAEf,OAAO;gBAAE,MAAM;oBAAE,MAAM;gBAAQ;gBAAG,OAAO;YAAK;QAChD;IACF;IAEA,MAAM,CAAC,QAAkB,CAAC;YACxB,QAAQ,CAAC,UAAqB,CAAC;oBAC7B,IAAI,CAAC,QAAgB,QAAe,CAAC;4BACnC,QAAQ,IAAM,QAAQ,OAAO,CAAC;oCAAE,MAAM;oCAAM,OAAO;gCAAK;wBAC1D,CAAC;oBACD,OAAO,CAAC,SAAmB,QAAQ,OAAO,CAAC;4BAAE,MAAM,EAAE;4BAAE,OAAO;wBAAK;gBACrE,CAAC;YACD,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACjE,QAAQ,CAAC,OAAc,CAAC;oBACtB,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,IAAM,CAAC;oBACb,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,CAAC,OAAc,CAAC;oBACtB,QAAQ,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAC1D,CAAC;QACH,CAAC;AACH;AAGO,MAAM,cAAc;IACzB;IACA,OAAO;AACT;AAGO,MAAM,qBAAqB;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/learning/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { getSupabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\ninterface LearningData {\n  id?: string\n  date: string\n  laravel_time: number\n  nodejs_time: number\n  nextjs_time: number\n  flutter_time: number\n  database_time: number\n  laravel_progress: number\n  nodejs_progress: number\n  nextjs_progress: number\n  flutter_progress: number\n  database_progress: number\n  learning_notes: string\n}\n\nexport default function LearningPage() {\n  const [user, setUser] = useState<any>(null)\n  const [learningData, setLearningData] = useState<LearningData>({\n    date: new Date().toISOString().split('T')[0],\n    laravel_time: 0,\n    nodejs_time: 0,\n    nextjs_time: 0,\n    flutter_time: 0,\n    database_time: 0,\n    laravel_progress: 0,\n    nodejs_progress: 0,\n    nextjs_progress: 0,\n    flutter_progress: 0,\n    database_progress: 0,\n    learning_notes: ''\n  })\n  const [loading, setLoading] = useState(true)\n  const [saving, setSaving] = useState(false)\n  const router = useRouter()\n\n  useEffect(() => {\n    checkUser()\n  }, [])\n\n  useEffect(() => {\n    if (user) {\n      loadLearningData()\n    }\n  }, [user, learningData.date])\n\n  const checkUser = async () => {\n    const supabase = await getSupabase()\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) {\n      router.push('/login')\n    } else {\n      setUser(user)\n    }\n    setLoading(false)\n  }\n\n  const loadLearningData = async () => {\n    try {\n      // Load from localStorage\n      const storageKey = `learning_${user.id}_${learningData.date}`\n      const stored = localStorage.getItem(storageKey)\n\n      if (stored) {\n        const data = JSON.parse(stored)\n        setLearningData({\n          id: data.id,\n          date: data.date,\n          laravel_time: data.laravel_time || 0,\n          nodejs_time: data.nodejs_time || 0,\n          nextjs_time: data.nextjs_time || 0,\n          flutter_time: data.flutter_time || 0,\n          database_time: data.database_time || 0,\n          laravel_progress: data.laravel_progress || 0,\n          nodejs_progress: data.nodejs_progress || 0,\n          nextjs_progress: data.nextjs_progress || 0,\n          flutter_progress: data.flutter_progress || 0,\n          database_progress: data.database_progress || 0,\n          learning_notes: data.learning_notes || ''\n        })\n      }\n    } catch (error) {\n      console.error('Error loading learning data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const calculateTotalHours = () => {\n    return (\n      learningData.laravel_time +\n      learningData.nodejs_time +\n      learningData.nextjs_time +\n      learningData.flutter_time +\n      learningData.database_time\n    ) / 60 // Convert minutes to hours\n  }\n\n  const handleSave = async () => {\n    setSaving(true)\n    try {\n      const totalHours = calculateTotalHours()\n      const dataToSave = {\n        id: learningData.id || `learning_${Date.now()}`,\n        user_id: user.id,\n        date: learningData.date,\n        laravel_time: learningData.laravel_time,\n        nodejs_time: learningData.nodejs_time,\n        nextjs_time: learningData.nextjs_time,\n        flutter_time: learningData.flutter_time,\n        database_time: learningData.database_time,\n        laravel_progress: learningData.laravel_progress,\n        nodejs_progress: learningData.nodejs_progress,\n        nextjs_progress: learningData.nextjs_progress,\n        flutter_progress: learningData.flutter_progress,\n        database_progress: learningData.database_progress,\n        total_hours: totalHours,\n        learning_notes: learningData.learning_notes,\n        created_at: new Date().toISOString()\n      }\n\n      // Save to localStorage\n      const storageKey = `learning_${user.id}_${learningData.date}`\n      localStorage.setItem(storageKey, JSON.stringify(dataToSave))\n\n      // Update state with new ID if it was a new record\n      if (!learningData.id) {\n        setLearningData(prev => ({ ...prev, id: dataToSave.id }))\n      }\n\n      alert('تم حفظ البيانات بنجاح!')\n    } catch (error) {\n      console.error('Error saving learning data:', error)\n      alert('حدث خطأ أثناء حفظ البيانات')\n    } finally {\n      setSaving(false)\n    }\n  }\n\n  const handleTimeChange = (field: keyof LearningData, value: number) => {\n    setLearningData(prev => ({ ...prev, [field]: value }))\n  }\n\n  const handleProgressChange = (field: keyof LearningData, value: number) => {\n    setLearningData(prev => ({ ...prev, [field]: value }))\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري التحميل...</p>\n        </div>\n      </div>\n    )\n  }\n\n  const totalHours = calculateTotalHours()\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-blue-600 hover:text-blue-800 ml-4\">\n                ← العودة للوحة الرئيسية\n              </Link>\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                📚 متابعة التعلم والتطوير\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <div className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium\">\n                إجمالي: {totalHours.toFixed(1)} ساعة\n              </div>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        <div className=\"bg-white rounded-lg shadow\">\n          <div className=\"p-6\">\n            {/* Date Selector */}\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                التاريخ\n              </label>\n              <input\n                type=\"date\"\n                value={learningData.date}\n                onChange={(e) => setLearningData(prev => ({ ...prev, date: e.target.value }))}\n                className=\"px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n\n            {/* Learning Sections */}\n            <div className=\"space-y-8\">\n              {/* Laravel */}\n              <div className=\"border-b pb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <span className=\"text-2xl ml-2\">🔴</span>\n                  Laravel\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      الوقت المستغرق (دقيقة)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      value={learningData.laravel_time}\n                      onChange={(e) => handleTimeChange('laravel_time', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      نسبة التقدم (%)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      max=\"100\"\n                      value={learningData.laravel_progress}\n                      onChange={(e) => handleProgressChange('laravel_progress', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Node.js */}\n              <div className=\"border-b pb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <span className=\"text-2xl ml-2\">🟢</span>\n                  Node.js\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      الوقت المستغرق (دقيقة)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      value={learningData.nodejs_time}\n                      onChange={(e) => handleTimeChange('nodejs_time', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      نسبة التقدم (%)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      max=\"100\"\n                      value={learningData.nodejs_progress}\n                      onChange={(e) => handleProgressChange('nodejs_progress', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Next.js */}\n              <div className=\"border-b pb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <span className=\"text-2xl ml-2\">⚫</span>\n                  Next.js\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      الوقت المستغرق (دقيقة)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      value={learningData.nextjs_time}\n                      onChange={(e) => handleTimeChange('nextjs_time', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      نسبة التقدم (%)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      max=\"100\"\n                      value={learningData.nextjs_progress}\n                      onChange={(e) => handleProgressChange('nextjs_progress', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Flutter */}\n              <div className=\"border-b pb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <span className=\"text-2xl ml-2\">🔵</span>\n                  Flutter\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      الوقت المستغرق (دقيقة)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      value={learningData.flutter_time}\n                      onChange={(e) => handleTimeChange('flutter_time', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      نسبة التقدم (%)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      max=\"100\"\n                      value={learningData.flutter_progress}\n                      onChange={(e) => handleProgressChange('flutter_progress', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Database */}\n              <div className=\"border-b pb-6\">\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4 flex items-center\">\n                  <span className=\"text-2xl ml-2\">🗄️</span>\n                  قواعد البيانات\n                </h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      الوقت المستغرق (دقيقة)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      value={learningData.database_time}\n                      onChange={(e) => handleTimeChange('database_time', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      نسبة التقدم (%)\n                    </label>\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      max=\"100\"\n                      value={learningData.database_progress}\n                      onChange={(e) => handleProgressChange('database_progress', parseInt(e.target.value) || 0)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n              </div>\n\n              {/* Learning Notes */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  ملاحظات التعلم\n                </label>\n                <textarea\n                  value={learningData.learning_notes}\n                  onChange={(e) => setLearningData(prev => ({ ...prev, learning_notes: e.target.value }))}\n                  rows={4}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  placeholder=\"اكتب ملاحظاتك حول التعلم اليوم...\"\n                />\n              </div>\n            </div>\n\n            <div className=\"mt-8 pt-6 border-t\">\n              <button\n                onClick={handleSave}\n                disabled={saving}\n                className=\"w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50\"\n              >\n                {saving ? 'جاري الحفظ...' : 'حفظ البيانات'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAuBe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,MAAM,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,cAAc;QACd,aAAa;QACb,aAAa;QACb,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,gBAAgB;IAClB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM,aAAa,IAAI;KAAC;IAE5B,MAAM,YAAY;QAChB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;QACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;QACd,OAAO;YACL,QAAQ;QACV;QACA,WAAW;IACb;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,yBAAyB;YACzB,MAAM,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,aAAa,IAAI,EAAE;YAC7D,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,QAAQ;gBACV,MAAM,OAAO,KAAK,KAAK,CAAC;gBACxB,gBAAgB;oBACd,IAAI,KAAK,EAAE;oBACX,MAAM,KAAK,IAAI;oBACf,cAAc,KAAK,YAAY,IAAI;oBACnC,aAAa,KAAK,WAAW,IAAI;oBACjC,aAAa,KAAK,WAAW,IAAI;oBACjC,cAAc,KAAK,YAAY,IAAI;oBACnC,eAAe,KAAK,aAAa,IAAI;oBACrC,kBAAkB,KAAK,gBAAgB,IAAI;oBAC3C,iBAAiB,KAAK,eAAe,IAAI;oBACzC,iBAAiB,KAAK,eAAe,IAAI;oBACzC,kBAAkB,KAAK,gBAAgB,IAAI;oBAC3C,mBAAmB,KAAK,iBAAiB,IAAI;oBAC7C,gBAAgB,KAAK,cAAc,IAAI;gBACzC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB;QAC1B,OAAO,CACL,aAAa,YAAY,GACzB,aAAa,WAAW,GACxB,aAAa,WAAW,GACxB,aAAa,YAAY,GACzB,aAAa,aAAa,AAC5B,IAAI,GAAG,2BAA2B;;IACpC;IAEA,MAAM,aAAa;QACjB,UAAU;QACV,IAAI;YACF,MAAM,aAAa;YACnB,MAAM,aAAa;gBACjB,IAAI,aAAa,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;gBAC/C,SAAS,KAAK,EAAE;gBAChB,MAAM,aAAa,IAAI;gBACvB,cAAc,aAAa,YAAY;gBACvC,aAAa,aAAa,WAAW;gBACrC,aAAa,aAAa,WAAW;gBACrC,cAAc,aAAa,YAAY;gBACvC,eAAe,aAAa,aAAa;gBACzC,kBAAkB,aAAa,gBAAgB;gBAC/C,iBAAiB,aAAa,eAAe;gBAC7C,iBAAiB,aAAa,eAAe;gBAC7C,kBAAkB,aAAa,gBAAgB;gBAC/C,mBAAmB,aAAa,iBAAiB;gBACjD,aAAa;gBACb,gBAAgB,aAAa,cAAc;gBAC3C,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,uBAAuB;YACvB,MAAM,aAAa,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,aAAa,IAAI,EAAE;YAC7D,aAAa,OAAO,CAAC,YAAY,KAAK,SAAS,CAAC;YAEhD,kDAAkD;YAClD,IAAI,CAAC,aAAa,EAAE,EAAE;gBACpB,gBAAgB,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,IAAI,WAAW,EAAE;oBAAC,CAAC;YACzD;YAEA,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,mBAAmB,CAAC,OAA2B;QACnD,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACtD;IAEA,MAAM,uBAAuB,CAAC,OAA2B;QACvD,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IACtD;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,MAAM,aAAa;IAEnB,qBACE,8OAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAyC;;;;;;kDAG3E,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;wCAAuE;wCAC3E,WAAW,OAAO,CAAC;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzC,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,8OAAC;wCACC,MAAK;wCACL,OAAO,aAAa,IAAI;wCACxB,UAAU,CAAC,IAAM,gBAAgB,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC,CAAC;wCAC3E,WAAU;;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAS;;;;;;;0DAG3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,OAAO,aAAa,YAAY;gEAChC,UAAU,CAAC,IAAM,iBAAiB,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC9E,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,OAAO,aAAa,gBAAgB;gEACpC,UAAU,CAAC,IAAM,qBAAqB,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACtF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAS;;;;;;;0DAG3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,OAAO,aAAa,WAAW;gEAC/B,UAAU,CAAC,IAAM,iBAAiB,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC7E,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,OAAO,aAAa,eAAe;gEACnC,UAAU,CAAC,IAAM,qBAAqB,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACrF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAQ;;;;;;;0DAG1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,OAAO,aAAa,WAAW;gEAC/B,UAAU,CAAC,IAAM,iBAAiB,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC7E,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,OAAO,aAAa,eAAe;gEACnC,UAAU,CAAC,IAAM,qBAAqB,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACrF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAS;;;;;;;0DAG3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,OAAO,aAAa,YAAY;gEAChC,UAAU,CAAC,IAAM,iBAAiB,gBAAgB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC9E,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,OAAO,aAAa,gBAAgB;gEACpC,UAAU,CAAC,IAAM,qBAAqB,oBAAoB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACtF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;oDAAU;;;;;;;0DAG5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,OAAO,aAAa,aAAa;gEACjC,UAAU,CAAC,IAAM,iBAAiB,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEAC/E,WAAU;;;;;;;;;;;;kEAGd,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,8OAAC;gEACC,MAAK;gEACL,KAAI;gEACJ,KAAI;gEACJ,OAAO,aAAa,iBAAiB;gEACrC,UAAU,CAAC,IAAM,qBAAqB,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gEACvF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAOlB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,8OAAC;gDACC,OAAO,aAAa,cAAc;gDAClC,UAAU,CAAC,IAAM,gBAAgB,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACrF,MAAM;gDACN,WAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,SAAS,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}]}