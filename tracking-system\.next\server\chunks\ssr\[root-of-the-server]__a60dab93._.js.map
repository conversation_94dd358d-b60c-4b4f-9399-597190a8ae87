{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/lib/supabase.ts"], "sourcesContent": ["// Temporary authentication system using localStorage\ninterface User {\n  id: string\n  email: string\n  full_name?: string\n  created_at: string\n}\n\ninterface AuthResponse {\n  data: { user: User | null }\n  error: any\n}\n\n// Mock database using localStorage\nconst getStoredUsers = (): User[] => {\n  if (typeof window === 'undefined') return []\n  const users = localStorage.getItem('tracking_users')\n  return users ? JSON.parse(users) : []\n}\n\nconst saveUsers = (users: User[]) => {\n  if (typeof window === 'undefined') return\n  localStorage.setItem('tracking_users', JSON.stringify(users))\n}\n\nconst getCurrentUser = (): User | null => {\n  if (typeof window === 'undefined') return null\n  const user = localStorage.getItem('tracking_current_user')\n  return user ? JSON.parse(user) : null\n}\n\nconst setCurrentUser = (user: User | null) => {\n  if (typeof window === 'undefined') return\n  if (user) {\n    localStorage.setItem('tracking_current_user', JSON.stringify(user))\n  } else {\n    localStorage.removeItem('tracking_current_user')\n  }\n}\n\n// Initialize demo users if none exist\nconst initializeDemoUsers = () => {\n  if (typeof window === 'undefined') return\n  const existingUsers = getStoredUsers()\n  if (existingUsers.length === 0) {\n    const demoUsers = [\n      {\n        id: 'demo-user-1',\n        email: '<EMAIL>',\n        full_name: 'مستخدم تجريبي',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'user-test-1',\n        email: '<EMAIL>',\n        full_name: 'سمير الكحلان',\n        created_at: new Date().toISOString()\n      }\n    ]\n    saveUsers(demoUsers)\n  }\n}\n\n// Mock Supabase client\nconst mockSupabase = {\n  auth: {\n    getUser: async (): Promise<AuthResponse> => {\n      const user = getCurrentUser()\n      return { data: { user }, error: null }\n    },\n\n    signOut: async () => {\n      setCurrentUser(null)\n      return { error: null }\n    },\n\n    signInWithPassword: async ({ email, password }: { email: string, password: string }) => {\n      const users = getStoredUsers()\n      const user = users.find(u => u.email === email)\n\n      if (!user) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني غير مسجل' }\n        }\n      }\n\n      // For demo, accept any password\n      setCurrentUser(user)\n      return { data: { user }, error: null }\n    },\n\n    signUp: async ({ email, password, options }: {\n      email: string,\n      password: string,\n      options?: { data?: { full_name?: string } }\n    }) => {\n      const users = getStoredUsers()\n      const existingUser = users.find(u => u.email === email)\n\n      if (existingUser) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني مسجل بالفعل' }\n        }\n      }\n\n      const newUser: User = {\n        id: Date.now().toString(),\n        email,\n        full_name: options?.data?.full_name,\n        created_at: new Date().toISOString()\n      }\n\n      users.push(newUser)\n      saveUsers(users)\n      setCurrentUser(newUser)\n\n      return { data: { user: newUser }, error: null }\n    }\n  },\n\n  from: (table: string) => ({\n    select: (columns?: string) => ({\n      eq: (column: string, value: any) => ({\n        single: () => Promise.resolve({ data: null, error: null })\n      }),\n      order: (column: string) => Promise.resolve({ data: [], error: null })\n    }),\n    insert: (data: any) => Promise.resolve({ data: null, error: null }),\n    update: (data: any) => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    delete: () => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    upsert: (data: any) => ({\n      select: () => Promise.resolve({ data: null, error: null })\n    })\n  })\n}\n\n// For client-side operations\nexport const getSupabase = async () => {\n  initializeDemoUsers()\n  return mockSupabase\n}\n\n// For server-side operations\nexport const createServerClient = async () => {\n  return mockSupabase\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WorshipRecord {\n  id: string\n  user_id: string\n  date: string\n  sleep_on_time: boolean\n  wake_up_on_time: boolean\n  night_prayer: boolean\n  fajr_prayer: boolean\n  quran_reading: boolean\n  istighfar_200: boolean\n  salawat_200: boolean\n  tasbih_100: boolean\n  la_ilaha_illa_allah_100: boolean\n  la_ilaha_illa_anta_50: boolean\n  morning_exercise: boolean\n  shower: boolean\n  duha_prayer: boolean\n  dhuhr_prayer: boolean\n  asr_prayer: boolean\n  maghrib_prayer: boolean\n  isha_prayer: boolean\n  slept_after_fajr: boolean\n  worship_percentage: number\n  spiritual_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LearningRecord {\n  id: string\n  user_id: string\n  date: string\n  laravel_time: number\n  laravel_topic?: string\n  laravel_progress: number\n  laravel_rating: number\n  nodejs_time: number\n  nodejs_topic?: string\n  nodejs_progress: number\n  nextjs_time: number\n  nextjs_topic?: string\n  nextjs_progress: number\n  mean_rating: number\n  flutter_time: number\n  flutter_topic?: string\n  flutter_progress: number\n  database_time: number\n  database_type?: string\n  database_progress: number\n  total_hours: number\n  key_learnings?: string\n  challenges?: string\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TemplateRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_time: number\n  shopify_features?: string\n  shopify_progress: number\n  shopify_issues?: string\n  shopify_solutions?: string\n  salla_progress: number\n  salla_notes?: string\n  wordpress_progress: number\n  wordpress_notes?: string\n  platform_api?: string\n  platform_functions?: string\n  platform_testing: boolean\n  wholesale_prices?: string\n  expected_revenue: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AppRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_functions?: string\n  shopify_integration: string\n  shopify_api_test: string\n  shopify_status: string\n  salla_functions?: string\n  salla_integration: string\n  salla_api_test: string\n  salla_status: string\n  wordpress_functions?: string\n  wordpress_integration: string\n  wordpress_api_test: string\n  wordpress_status: string\n  future_app_ideas?: string\n  future_app_planning?: string\n  future_app_features?: string\n  integration_issues?: string\n  price_updates?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ProjectRecord {\n  id: string\n  user_id: string\n  date: string\n  period1_project?: string\n  period1_tasks?: string\n  period1_time: number\n  period2_project?: string\n  period2_tasks?: string\n  period2_time: number\n  dashsy_tasks?: string\n  dashsy_progress: number\n  dashsy_issues?: string\n  dashsy_solutions?: string\n  dashsy_tomorrow?: string\n  syriana_tasks?: string\n  syriana_progress: number\n  syriana_issues?: string\n  syriana_solutions?: string\n  syriana_tomorrow?: string\n  booking_tasks?: string\n  booking_progress: number\n  booking_issues?: string\n  booking_solutions?: string\n  booking_tomorrow?: string\n  total_hours: number\n  productivity_rating: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FinanceRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_revenue: number\n  salla_revenue: number\n  wordpress_revenue: number\n  apps_revenue: number\n  projects_revenue: number\n  total_revenue: number\n  daily_expenses: number\n  net_profit: number\n  monthly_cumulative: number\n  financial_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WeeklyRecord {\n  id: string\n  user_id: string\n  week_start: string\n  week_end: string\n  routine_days: number\n  worship_percentage: number\n  total_learning_hours: number\n  key_topics?: string\n  laravel_progress: number\n  mean_progress: number\n  flutter_progress: number\n  database_progress: number\n  shopify_progress: number\n  platform_progress: number\n  apps_progress: number\n  dashsy_progress: number\n  syriana_progress: number\n  booking_progress: number\n  weekly_revenue: number\n  main_challenges?: string\n  main_solutions?: string\n  next_week_plan?: string\n  overall_rating: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface DashboardRecord {\n  id: string\n  user_id: string\n  date: string\n  routine_status: 'green' | 'yellow' | 'red'\n  learning_progress: number\n  templates_progress: number\n  projects_progress: number\n  daily_revenue: number\n  monthly_routine_percentage: number\n  monthly_learning_hours: number\n  monthly_templates_progress: number\n  monthly_projects_progress: number\n  urgent_alerts?: string\n  quick_notes?: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAarD,mCAAmC;AACnC,MAAM,iBAAiB;IACrB,wCAAmC,OAAO,EAAE;;IAC5C,MAAM;AAER;AAEA,MAAM,YAAY,CAAC;IACjB,wCAAmC;;AAErC;AAEA,MAAM,iBAAiB;IACrB,wCAAmC,OAAO;;IAC1C,MAAM;AAER;AAEA,MAAM,iBAAiB,CAAC;IACtB,wCAAmC;;AAMrC;AAEA,sCAAsC;AACtC,MAAM,sBAAsB;IAC1B,wCAAmC;;IACnC,MAAM;AAkBR;AAEA,uBAAuB;AACvB,MAAM,eAAe;IACnB,MAAM;QACJ,SAAS;YACP,MAAM,OAAO;YACb,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,SAAS;YACP,eAAe;YACf,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,oBAAoB,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACjF,MAAM,QAAQ;YACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEzC,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAA6B;gBACjD;YACF;YAEA,gCAAgC;YAChC,eAAe;YACf,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,QAAQ,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAIxC;YACC,MAAM,QAAQ;YACd,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEjD,IAAI,cAAc;gBAChB,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAAgC;gBACpD;YACF;YAEA,MAAM,UAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB;gBACA,WAAW,SAAS,MAAM;gBAC1B,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,IAAI,CAAC;YACX,UAAU;YACV,eAAe;YAEf,OAAO;gBAAE,MAAM;oBAAE,MAAM;gBAAQ;gBAAG,OAAO;YAAK;QAChD;IACF;IAEA,MAAM,CAAC,QAAkB,CAAC;YACxB,QAAQ,CAAC,UAAqB,CAAC;oBAC7B,IAAI,CAAC,QAAgB,QAAe,CAAC;4BACnC,QAAQ,IAAM,QAAQ,OAAO,CAAC;oCAAE,MAAM;oCAAM,OAAO;gCAAK;wBAC1D,CAAC;oBACD,OAAO,CAAC,SAAmB,QAAQ,OAAO,CAAC;4BAAE,MAAM,EAAE;4BAAE,OAAO;wBAAK;gBACrE,CAAC;YACD,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACjE,QAAQ,CAAC,OAAc,CAAC;oBACtB,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,IAAM,CAAC;oBACb,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,CAAC,OAAc,CAAC;oBACtB,QAAQ,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAC1D,CAAC;QACH,CAAC;AACH;AAGO,MAAM,cAAc;IACzB;IACA,OAAO;AACT;AAGO,MAAM,qBAAqB;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/analytics/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { getSupabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport {\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  BarChart,\n  Bar,\n  PieChart,\n  Pie,\n  Cell,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  Tooltip,\n  Legend,\n  ResponsiveContainer\n} from 'recharts'\n\ninterface AnalyticsData {\n  worshipTrend: any[]\n  learningTrend: any[]\n  revenueTrend: any[]\n  projectProgress: any[]\n  monthlyStats: any\n}\n\nexport default function AnalyticsPage() {\n  const [user, setUser] = useState<any>(null)\n  const [data, setData] = useState<AnalyticsData>({\n    worshipTrend: [],\n    learningTrend: [],\n    revenueTrend: [],\n    projectProgress: [],\n    monthlyStats: {}\n  })\n  const [loading, setLoading] = useState(true)\n  const [timeRange, setTimeRange] = useState('30') // days\n  const router = useRouter()\n\n  useEffect(() => {\n    checkUser()\n  }, [])\n\n  useEffect(() => {\n    if (user) {\n      loadAnalyticsData()\n    }\n  }, [user, timeRange])\n\n  const checkUser = async () => {\n    const supabase = await getSupabase()\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) {\n      router.push('/login')\n    } else {\n      setUser(user)\n    }\n    setLoading(false)\n  }\n\n  const loadAnalyticsData = async () => {\n    try {\n      const endDate = new Date()\n      const startDate = new Date()\n      startDate.setDate(endDate.getDate() - parseInt(timeRange))\n\n      const startDateStr = startDate.toISOString().split('T')[0]\n      const endDateStr = endDate.toISOString().split('T')[0]\n\n      // Load worship trend\n      const { data: worshipData } = await supabase\n        .from('worship_records')\n        .select('date, worship_percentage')\n        .eq('user_id', user.id)\n        .gte('date', startDateStr)\n        .lte('date', endDateStr)\n        .order('date')\n\n      // Load learning trend\n      const { data: learningData } = await supabase\n        .from('learning_records')\n        .select('date, total_hours, laravel_time, nodejs_time, nextjs_time, flutter_time, database_time')\n        .eq('user_id', user.id)\n        .gte('date', startDateStr)\n        .lte('date', endDateStr)\n        .order('date')\n\n      // Load revenue trend\n      const { data: revenueData } = await supabase\n        .from('finance_records')\n        .select('date, total_revenue, net_profit')\n        .eq('user_id', user.id)\n        .gte('date', startDateStr)\n        .lte('date', endDateStr)\n        .order('date')\n\n      // Load project progress\n      const { data: projectData } = await supabase\n        .from('project_records')\n        .select('date, dashsy_progress, syriana_progress, booking_progress')\n        .eq('user_id', user.id)\n        .gte('date', startDateStr)\n        .lte('date', endDateStr)\n        .order('date')\n\n      setData({\n        worshipTrend: worshipData || [],\n        learningTrend: learningData?.map(item => ({\n          ...item,\n          Laravel: item.laravel_time,\n          'Node.js': item.nodejs_time,\n          'Next.js': item.nextjs_time,\n          Flutter: item.flutter_time,\n          Database: item.database_time\n        })) || [],\n        revenueTrend: revenueData || [],\n        projectProgress: projectData?.map(item => ({\n          ...item,\n          'Dash-sy': item.dashsy_progress,\n          'Syriana Store': item.syriana_progress,\n          'Booking-Sy': item.booking_progress\n        })) || [],\n        monthlyStats: {\n          totalWorshipDays: worshipData?.length || 0,\n          avgWorshipPercentage: worshipData?.reduce((sum, item) => sum + item.worship_percentage, 0) / (worshipData?.length || 1) || 0,\n          totalLearningHours: learningData?.reduce((sum, item) => sum + item.total_hours, 0) || 0,\n          totalRevenue: revenueData?.reduce((sum, item) => sum + item.total_revenue, 0) || 0,\n          totalProfit: revenueData?.reduce((sum, item) => sum + item.net_profit, 0) || 0\n        }\n      })\n    } catch (error) {\n      console.error('Error loading analytics data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري تحميل التحليلات...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-blue-600 hover:text-blue-800 ml-4\">\n                ← العودة للوحة الرئيسية\n              </Link>\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                📈 التحليلات والإحصائيات\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <select\n                value={timeRange}\n                onChange={(e) => setTimeRange(e.target.value)}\n                className=\"px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"7\">آخر 7 أيام</option>\n                <option value=\"30\">آخر 30 يوم</option>\n                <option value=\"90\">آخر 3 أشهر</option>\n                <option value=\"365\">آخر سنة</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        {/* Summary Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">🕌</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">متوسط العبادات</p>\n                <p className=\"text-2xl font-bold text-blue-600\">\n                  {Math.round(data.monthlyStats.avgWorshipPercentage)}%\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">📚</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">إجمالي ساعات التعلم</p>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {Math.round(data.monthlyStats.totalLearningHours)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">💰</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">إجمالي الإيرادات</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">\n                  ${Math.round(data.monthlyStats.totalRevenue)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">📊</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">صافي الربح</p>\n                <p className=\"text-2xl font-bold text-purple-600\">\n                  ${Math.round(data.monthlyStats.totalProfit)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Charts */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Worship Trend */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">اتجاه العبادات</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={data.worshipTrend}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"worship_percentage\" \n                  stroke=\"#3B82F6\" \n                  strokeWidth={2}\n                  name=\"نسبة العبادات %\"\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Learning Hours */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ساعات التعلم اليومية</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <AreaChart data={data.learningTrend}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"Laravel\" \n                  stackId=\"1\" \n                  stroke=\"#3B82F6\" \n                  fill=\"#3B82F6\"\n                />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"Node.js\" \n                  stackId=\"1\" \n                  stroke=\"#10B981\" \n                  fill=\"#10B981\"\n                />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"Next.js\" \n                  stackId=\"1\" \n                  stroke=\"#F59E0B\" \n                  fill=\"#F59E0B\"\n                />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"Flutter\" \n                  stackId=\"1\" \n                  stroke=\"#EF4444\" \n                  fill=\"#EF4444\"\n                />\n                <Area \n                  type=\"monotone\" \n                  dataKey=\"Database\" \n                  stackId=\"1\" \n                  stroke=\"#8B5CF6\" \n                  fill=\"#8B5CF6\"\n                />\n              </AreaChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Revenue Trend */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">اتجاه الإيرادات</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <BarChart data={data.revenueTrend}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Bar dataKey=\"total_revenue\" fill=\"#10B981\" name=\"إجمالي الإيرادات\" />\n                <Bar dataKey=\"net_profit\" fill=\"#3B82F6\" name=\"صافي الربح\" />\n              </BarChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Project Progress */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تقدم المشاريع</h3>\n            <ResponsiveContainer width=\"100%\" height={300}>\n              <LineChart data={data.projectProgress}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"date\" />\n                <YAxis />\n                <Tooltip />\n                <Legend />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"Dash-sy\" \n                  stroke=\"#3B82F6\" \n                  strokeWidth={2}\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"Syriana Store\" \n                  stroke=\"#10B981\" \n                  strokeWidth={2}\n                />\n                <Line \n                  type=\"monotone\" \n                  dataKey=\"Booking-Sy\" \n                  stroke=\"#F59E0B\" \n                  strokeWidth={2}\n                />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;;;;AALA;;;;;;;AAgCe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;QAC9C,cAAc,EAAE;QAChB,eAAe,EAAE;QACjB,cAAc,EAAE;QAChB,iBAAiB,EAAE;QACnB,cAAc,CAAC;IACjB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO;;IACxD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM;KAAU;IAEpB,MAAM,YAAY;QAChB,MAAM,YAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD;QACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,UAAS,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;QACd,OAAO;YACL,QAAQ;QACV;QACA,WAAW;IACb;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,UAAU,IAAI;YACpB,MAAM,YAAY,IAAI;YACtB,UAAU,OAAO,CAAC,QAAQ,OAAO,KAAK,SAAS;YAE/C,MAAM,eAAe,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,MAAM,aAAa,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEtD,qBAAqB;YACrB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,4BACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,cACZ,GAAG,CAAC,QAAQ,YACZ,KAAK,CAAC;YAET,sBAAsB;YACtB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,oBACL,MAAM,CAAC,0FACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,cACZ,GAAG,CAAC,QAAQ,YACZ,KAAK,CAAC;YAET,qBAAqB;YACrB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,mCACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,cACZ,GAAG,CAAC,QAAQ,YACZ,KAAK,CAAC;YAET,wBAAwB;YACxB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,6DACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,cACZ,GAAG,CAAC,QAAQ,YACZ,KAAK,CAAC;YAET,QAAQ;gBACN,cAAc,eAAe,EAAE;gBAC/B,eAAe,cAAc,IAAI,CAAA,OAAQ,CAAC;wBACxC,GAAG,IAAI;wBACP,SAAS,KAAK,YAAY;wBAC1B,WAAW,KAAK,WAAW;wBAC3B,WAAW,KAAK,WAAW;wBAC3B,SAAS,KAAK,YAAY;wBAC1B,UAAU,KAAK,aAAa;oBAC9B,CAAC,MAAM,EAAE;gBACT,cAAc,eAAe,EAAE;gBAC/B,iBAAiB,aAAa,IAAI,CAAA,OAAQ,CAAC;wBACzC,GAAG,IAAI;wBACP,WAAW,KAAK,eAAe;wBAC/B,iBAAiB,KAAK,gBAAgB;wBACtC,cAAc,KAAK,gBAAgB;oBACrC,CAAC,MAAM,EAAE;gBACT,cAAc;oBACZ,kBAAkB,aAAa,UAAU;oBACzC,sBAAsB,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,kBAAkB,EAAE,KAAK,CAAC,aAAa,UAAU,CAAC,KAAK;oBAC3H,oBAAoB,cAAc,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE,MAAM;oBACtF,cAAc,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,aAAa,EAAE,MAAM;oBACjF,aAAa,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,MAAM;gBAC/E;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QAAC;QAAW;QAAW;QAAW;QAAW;QAAW;KAAU;IAEjF,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAyC;;;;;;kDAG3E,8OAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAItD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;sDAEV,8OAAC;4CAAO,OAAM;sDAAI;;;;;;sDAClB,8OAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,8OAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,8OAAC;4CAAO,OAAM;sDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,8OAAC;gBAAK,WAAU;;kCAEd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDACV,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,oBAAoB;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;0CAM5D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;8DACV,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0CAMxD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAqC;wDAC9C,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,8OAAC;oDAAE,WAAU;;wDAAqC;wDAC9C,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAoB,OAAM;wCAAO,QAAQ;kDACxC,cAAA,8OAAC;4CAAU,MAAM,KAAK,YAAY;;8DAChC,8OAAC;oDAAc,iBAAgB;;;;;;8DAC/B,8OAAC;oDAAM,SAAQ;;;;;;8DACf,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,QAAO;oDACP,aAAa;oDACb,MAAK;;;;;;;;;;;;;;;;;;;;;;;0CAOb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAoB,OAAM;wCAAO,QAAQ;kDACxC,cAAA,8OAAC;4CAAU,MAAM,KAAK,aAAa;;8DACjC,8OAAC;oDAAc,iBAAgB;;;;;;8DAC/B,8OAAC;oDAAM,SAAQ;;;;;;8DACf,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,SAAQ;oDACR,QAAO;oDACP,MAAK;;;;;;8DAEP,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,SAAQ;oDACR,QAAO;oDACP,MAAK;;;;;;8DAEP,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,SAAQ;oDACR,QAAO;oDACP,MAAK;;;;;;8DAEP,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,SAAQ;oDACR,QAAO;oDACP,MAAK;;;;;;8DAEP,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,SAAQ;oDACR,QAAO;oDACP,MAAK;;;;;;;;;;;;;;;;;;;;;;;0CAOb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAoB,OAAM;wCAAO,QAAQ;kDACxC,cAAA,8OAAC;4CAAS,MAAM,KAAK,YAAY;;8DAC/B,8OAAC;oDAAc,iBAAgB;;;;;;8DAC/B,8OAAC;oDAAM,SAAQ;;;;;;8DACf,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;oDAAI,SAAQ;oDAAgB,MAAK;oDAAU,MAAK;;;;;;8DACjD,8OAAC;oDAAI,SAAQ;oDAAa,MAAK;oDAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;0CAMpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,8OAAC;wCAAoB,OAAM;wCAAO,QAAQ;kDACxC,cAAA,8OAAC;4CAAU,MAAM,KAAK,eAAe;;8DACnC,8OAAC;oDAAc,iBAAgB;;;;;;8DAC/B,8OAAC;oDAAM,SAAQ;;;;;;8DACf,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;;;;;8DACD,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,QAAO;oDACP,aAAa;;;;;;8DAEf,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,QAAO;oDACP,aAAa;;;;;;8DAEf,8OAAC;oDACC,MAAK;oDACL,SAAQ;oDACR,QAAO;oDACP,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/B", "debugId": null}}]}