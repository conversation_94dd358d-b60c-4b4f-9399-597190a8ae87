{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/api/test-connection/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nexport async function GET() {\n  try {\n    // Test basic API functionality\n    const envCheck = {\n      hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,\n      hasSupabaseKey: !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,\n      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...',\n    }\n\n    return NextResponse.json({\n      success: true,\n      message: 'API is working correctly',\n      environment: envCheck,\n      timestamp: new Date().toISOString(),\n      nodeVersion: process.version\n    })\n\n  } catch (error) {\n    console.error('API test error:', error)\n    return NextResponse.json({\n      success: false,\n      message: 'API test failed',\n      error: error instanceof Error ? error.message : 'Unknown error'\n    }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,IAAI;QACF,+BAA+B;QAC/B,MAAM,WAAW;YACf,gBAAgB,CAAC;YACjB,gBAAgB,CAAC;YACjB,aAAa,8EAAsC,UAAU,GAAG,MAAM;QACxE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,aAAa;YACb,WAAW,IAAI,OAAO,WAAW;YACjC,aAAa,QAAQ,OAAO;QAC9B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mBAAmB;QACjC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD,GAAG;YAAE,QAAQ;QAAI;IACnB;AACF", "debugId": null}}]}