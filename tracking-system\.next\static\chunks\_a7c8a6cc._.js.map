{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/lib/supabase.ts"], "sourcesContent": ["// Temporary authentication system using localStorage\ninterface User {\n  id: string\n  email: string\n  full_name?: string\n  created_at: string\n}\n\ninterface AuthResponse {\n  data: { user: User | null }\n  error: any\n}\n\n// Mock database using localStorage\nconst getStoredUsers = (): User[] => {\n  if (typeof window === 'undefined') return []\n  const users = localStorage.getItem('tracking_users')\n  return users ? JSON.parse(users) : []\n}\n\nconst saveUsers = (users: User[]) => {\n  if (typeof window === 'undefined') return\n  localStorage.setItem('tracking_users', JSON.stringify(users))\n}\n\nconst getCurrentUser = (): User | null => {\n  if (typeof window === 'undefined') return null\n  const user = localStorage.getItem('tracking_current_user')\n  return user ? JSON.parse(user) : null\n}\n\nconst setCurrentUser = (user: User | null) => {\n  if (typeof window === 'undefined') return\n  if (user) {\n    localStorage.setItem('tracking_current_user', JSON.stringify(user))\n  } else {\n    localStorage.removeItem('tracking_current_user')\n  }\n}\n\n// Initialize demo users if none exist\nconst initializeDemoUsers = () => {\n  if (typeof window === 'undefined') return\n  const existingUsers = getStoredUsers()\n  if (existingUsers.length === 0) {\n    const demoUsers = [\n      {\n        id: 'demo-user-1',\n        email: '<EMAIL>',\n        full_name: 'مستخدم تجريبي',\n        created_at: new Date().toISOString()\n      },\n      {\n        id: 'user-test-1',\n        email: '<EMAIL>',\n        full_name: 'سمير الكحلان',\n        created_at: new Date().toISOString()\n      }\n    ]\n    saveUsers(demoUsers)\n  }\n}\n\n// Mock Supabase client\nconst mockSupabase = {\n  auth: {\n    getUser: async (): Promise<AuthResponse> => {\n      const user = getCurrentUser()\n      return { data: { user }, error: null }\n    },\n\n    signOut: async () => {\n      setCurrentUser(null)\n      return { error: null }\n    },\n\n    signInWithPassword: async ({ email, password }: { email: string, password: string }) => {\n      const users = getStoredUsers()\n      const user = users.find(u => u.email === email)\n\n      if (!user) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني غير مسجل' }\n        }\n      }\n\n      // For demo, accept any password\n      setCurrentUser(user)\n      return { data: { user }, error: null }\n    },\n\n    signUp: async ({ email, password, options }: {\n      email: string,\n      password: string,\n      options?: { data?: { full_name?: string } }\n    }) => {\n      const users = getStoredUsers()\n      const existingUser = users.find(u => u.email === email)\n\n      if (existingUser) {\n        return {\n          data: { user: null },\n          error: { message: 'البريد الإلكتروني مسجل بالفعل' }\n        }\n      }\n\n      const newUser: User = {\n        id: Date.now().toString(),\n        email,\n        full_name: options?.data?.full_name,\n        created_at: new Date().toISOString()\n      }\n\n      users.push(newUser)\n      saveUsers(users)\n      setCurrentUser(newUser)\n\n      return { data: { user: newUser }, error: null }\n    }\n  },\n\n  from: (table: string) => ({\n    select: (columns?: string) => ({\n      eq: (column: string, value: any) => ({\n        single: () => Promise.resolve({ data: null, error: null })\n      }),\n      order: (column: string) => Promise.resolve({ data: [], error: null })\n    }),\n    insert: (data: any) => Promise.resolve({ data: null, error: null }),\n    update: (data: any) => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    delete: () => ({\n      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })\n    }),\n    upsert: (data: any) => ({\n      select: () => Promise.resolve({ data: null, error: null })\n    })\n  })\n}\n\n// For client-side operations\nexport const getSupabase = async () => {\n  initializeDemoUsers()\n  return mockSupabase\n}\n\n// For server-side operations\nexport const createServerClient = async () => {\n  return mockSupabase\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WorshipRecord {\n  id: string\n  user_id: string\n  date: string\n  sleep_on_time: boolean\n  wake_up_on_time: boolean\n  night_prayer: boolean\n  fajr_prayer: boolean\n  quran_reading: boolean\n  istighfar_200: boolean\n  salawat_200: boolean\n  tasbih_100: boolean\n  la_ilaha_illa_allah_100: boolean\n  la_ilaha_illa_anta_50: boolean\n  morning_exercise: boolean\n  shower: boolean\n  duha_prayer: boolean\n  dhuhr_prayer: boolean\n  asr_prayer: boolean\n  maghrib_prayer: boolean\n  isha_prayer: boolean\n  slept_after_fajr: boolean\n  worship_percentage: number\n  spiritual_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LearningRecord {\n  id: string\n  user_id: string\n  date: string\n  laravel_time: number\n  laravel_topic?: string\n  laravel_progress: number\n  laravel_rating: number\n  nodejs_time: number\n  nodejs_topic?: string\n  nodejs_progress: number\n  nextjs_time: number\n  nextjs_topic?: string\n  nextjs_progress: number\n  mean_rating: number\n  flutter_time: number\n  flutter_topic?: string\n  flutter_progress: number\n  database_time: number\n  database_type?: string\n  database_progress: number\n  total_hours: number\n  key_learnings?: string\n  challenges?: string\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TemplateRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_time: number\n  shopify_features?: string\n  shopify_progress: number\n  shopify_issues?: string\n  shopify_solutions?: string\n  salla_progress: number\n  salla_notes?: string\n  wordpress_progress: number\n  wordpress_notes?: string\n  platform_api?: string\n  platform_functions?: string\n  platform_testing: boolean\n  wholesale_prices?: string\n  expected_revenue: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AppRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_functions?: string\n  shopify_integration: string\n  shopify_api_test: string\n  shopify_status: string\n  salla_functions?: string\n  salla_integration: string\n  salla_api_test: string\n  salla_status: string\n  wordpress_functions?: string\n  wordpress_integration: string\n  wordpress_api_test: string\n  wordpress_status: string\n  future_app_ideas?: string\n  future_app_planning?: string\n  future_app_features?: string\n  integration_issues?: string\n  price_updates?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ProjectRecord {\n  id: string\n  user_id: string\n  date: string\n  period1_project?: string\n  period1_tasks?: string\n  period1_time: number\n  period2_project?: string\n  period2_tasks?: string\n  period2_time: number\n  dashsy_tasks?: string\n  dashsy_progress: number\n  dashsy_issues?: string\n  dashsy_solutions?: string\n  dashsy_tomorrow?: string\n  syriana_tasks?: string\n  syriana_progress: number\n  syriana_issues?: string\n  syriana_solutions?: string\n  syriana_tomorrow?: string\n  booking_tasks?: string\n  booking_progress: number\n  booking_issues?: string\n  booking_solutions?: string\n  booking_tomorrow?: string\n  total_hours: number\n  productivity_rating: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FinanceRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_revenue: number\n  salla_revenue: number\n  wordpress_revenue: number\n  apps_revenue: number\n  projects_revenue: number\n  total_revenue: number\n  daily_expenses: number\n  net_profit: number\n  monthly_cumulative: number\n  financial_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WeeklyRecord {\n  id: string\n  user_id: string\n  week_start: string\n  week_end: string\n  routine_days: number\n  worship_percentage: number\n  total_learning_hours: number\n  key_topics?: string\n  laravel_progress: number\n  mean_progress: number\n  flutter_progress: number\n  database_progress: number\n  shopify_progress: number\n  platform_progress: number\n  apps_progress: number\n  dashsy_progress: number\n  syriana_progress: number\n  booking_progress: number\n  weekly_revenue: number\n  main_challenges?: string\n  main_solutions?: string\n  next_week_plan?: string\n  overall_rating: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface DashboardRecord {\n  id: string\n  user_id: string\n  date: string\n  routine_status: 'green' | 'yellow' | 'red'\n  learning_progress: number\n  templates_progress: number\n  projects_progress: number\n  daily_revenue: number\n  monthly_routine_percentage: number\n  monthly_learning_hours: number\n  monthly_templates_progress: number\n  monthly_projects_progress: number\n  urgent_alerts?: string\n  quick_notes?: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;AAarD,mCAAmC;AACnC,MAAM,iBAAiB;IACrB,uCAAmC;;IAAQ;IAC3C,MAAM,QAAQ,aAAa,OAAO,CAAC;IACnC,OAAO,QAAQ,KAAK,KAAK,CAAC,SAAS,EAAE;AACvC;AAEA,MAAM,YAAY,CAAC;IACjB,uCAAmC;;IAAK;IACxC,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;AACxD;AAEA,MAAM,iBAAiB;IACrB,uCAAmC;;IAAU;IAC7C,MAAM,OAAO,aAAa,OAAO,CAAC;IAClC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;AACnC;AAEA,MAAM,iBAAiB,CAAC;IACtB,uCAAmC;;IAAK;IACxC,IAAI,MAAM;QACR,aAAa,OAAO,CAAC,yBAAyB,KAAK,SAAS,CAAC;IAC/D,OAAO;QACL,aAAa,UAAU,CAAC;IAC1B;AACF;AAEA,sCAAsC;AACtC,MAAM,sBAAsB;IAC1B,uCAAmC;;IAAK;IACxC,MAAM,gBAAgB;IACtB,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,MAAM,YAAY;YAChB;gBACE,IAAI;gBACJ,OAAO;gBACP,WAAW;gBACX,YAAY,IAAI,OAAO,WAAW;YACpC;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,WAAW;gBACX,YAAY,IAAI,OAAO,WAAW;YACpC;SACD;QACD,UAAU;IACZ;AACF;AAEA,uBAAuB;AACvB,MAAM,eAAe;IACnB,MAAM;QACJ,SAAS;YACP,MAAM,OAAO;YACb,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,SAAS;YACP,eAAe;YACf,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,oBAAoB,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAuC;YACjF,MAAM,QAAQ;YACd,MAAM,OAAO,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEzC,IAAI,CAAC,MAAM;gBACT,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAA6B;gBACjD;YACF;YAEA,gCAAgC;YAChC,eAAe;YACf,OAAO;gBAAE,MAAM;oBAAE;gBAAK;gBAAG,OAAO;YAAK;QACvC;QAEA,QAAQ,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAIxC;YACC,MAAM,QAAQ;YACd,MAAM,eAAe,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,KAAK,KAAK;YAEjD,IAAI,cAAc;gBAChB,OAAO;oBACL,MAAM;wBAAE,MAAM;oBAAK;oBACnB,OAAO;wBAAE,SAAS;oBAAgC;gBACpD;YACF;YAEA,MAAM,UAAgB;gBACpB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB;gBACA,WAAW,SAAS,MAAM;gBAC1B,YAAY,IAAI,OAAO,WAAW;YACpC;YAEA,MAAM,IAAI,CAAC;YACX,UAAU;YACV,eAAe;YAEf,OAAO;gBAAE,MAAM;oBAAE,MAAM;gBAAQ;gBAAG,OAAO;YAAK;QAChD;IACF;IAEA,MAAM,CAAC,QAAkB,CAAC;YACxB,QAAQ,CAAC,UAAqB,CAAC;oBAC7B,IAAI,CAAC,QAAgB,QAAe,CAAC;4BACnC,QAAQ,IAAM,QAAQ,OAAO,CAAC;oCAAE,MAAM;oCAAM,OAAO;gCAAK;wBAC1D,CAAC;oBACD,OAAO,CAAC,SAAmB,QAAQ,OAAO,CAAC;4BAAE,MAAM,EAAE;4BAAE,OAAO;wBAAK;gBACrE,CAAC;YACD,QAAQ,CAAC,OAAc,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACjE,QAAQ,CAAC,OAAc,CAAC;oBACtB,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,IAAM,CAAC;oBACb,IAAI,CAAC,QAAgB,QAAe,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAChF,CAAC;YACD,QAAQ,CAAC,OAAc,CAAC;oBACtB,QAAQ,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBAC1D,CAAC;QACH,CAAC;AACH;AAGO,MAAM,cAAc;IACzB;IACA,OAAO;AACT;AAGO,MAAM,qBAAqB;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/analytics/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\nimport { getSupabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n// Charts will be implemented later\n// import { LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts'\n\ninterface AnalyticsData {\n  worshipTrend: any[]\n  learningTrend: any[]\n  revenueTrend: any[]\n  projectProgress: any[]\n  monthlyStats: any\n}\n\nexport default function AnalyticsPage() {\n  const [user, setUser] = useState<any>(null)\n  const [data, setData] = useState<AnalyticsData>({\n    worshipTrend: [],\n    learningTrend: [],\n    revenueTrend: [],\n    projectProgress: [],\n    monthlyStats: {}\n  })\n  const [loading, setLoading] = useState(true)\n  const [timeRange, setTimeRange] = useState('30') // days\n  const router = useRouter()\n\n  useEffect(() => {\n    checkUser()\n  }, [])\n\n  useEffect(() => {\n    if (user) {\n      loadAnalyticsData()\n    }\n  }, [user, timeRange])\n\n  const checkUser = async () => {\n    const supabase = await getSupabase()\n    const { data: { user } } = await supabase.auth.getUser()\n    if (!user) {\n      router.push('/login')\n    } else {\n      setUser(user)\n    }\n    setLoading(false)\n  }\n\n  const loadAnalyticsData = async () => {\n    try {\n      const endDate = new Date()\n      const startDate = new Date()\n      startDate.setDate(endDate.getDate() - parseInt(timeRange))\n\n      const startDateStr = startDate.toISOString().split('T')[0]\n      const endDateStr = endDate.toISOString().split('T')[0]\n\n      // Load worship trend\n      const { data: worshipData } = await supabase\n        .from('worship_records')\n        .select('date, worship_percentage')\n        .eq('user_id', user.id)\n        .gte('date', startDateStr)\n        .lte('date', endDateStr)\n        .order('date')\n\n      // Load learning trend\n      const { data: learningData } = await supabase\n        .from('learning_records')\n        .select('date, total_hours, laravel_time, nodejs_time, nextjs_time, flutter_time, database_time')\n        .eq('user_id', user.id)\n        .gte('date', startDateStr)\n        .lte('date', endDateStr)\n        .order('date')\n\n      // Load revenue trend\n      const { data: revenueData } = await supabase\n        .from('finance_records')\n        .select('date, total_revenue, net_profit')\n        .eq('user_id', user.id)\n        .gte('date', startDateStr)\n        .lte('date', endDateStr)\n        .order('date')\n\n      // Load project progress\n      const { data: projectData } = await supabase\n        .from('project_records')\n        .select('date, dashsy_progress, syriana_progress, booking_progress')\n        .eq('user_id', user.id)\n        .gte('date', startDateStr)\n        .lte('date', endDateStr)\n        .order('date')\n\n      setData({\n        worshipTrend: worshipData || [],\n        learningTrend: learningData?.map(item => ({\n          ...item,\n          Laravel: item.laravel_time,\n          'Node.js': item.nodejs_time,\n          'Next.js': item.nextjs_time,\n          Flutter: item.flutter_time,\n          Database: item.database_time\n        })) || [],\n        revenueTrend: revenueData || [],\n        projectProgress: projectData?.map(item => ({\n          ...item,\n          'Dash-sy': item.dashsy_progress,\n          'Syriana Store': item.syriana_progress,\n          'Booking-Sy': item.booking_progress\n        })) || [],\n        monthlyStats: {\n          totalWorshipDays: worshipData?.length || 0,\n          avgWorshipPercentage: worshipData?.reduce((sum, item) => sum + item.worship_percentage, 0) / (worshipData?.length || 1) || 0,\n          totalLearningHours: learningData?.reduce((sum, item) => sum + item.total_hours, 0) || 0,\n          totalRevenue: revenueData?.reduce((sum, item) => sum + item.total_revenue, 0) || 0,\n          totalProfit: revenueData?.reduce((sum, item) => sum + item.net_profit, 0) || 0\n        }\n      })\n    } catch (error) {\n      console.error('Error loading analytics data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">جاري تحميل التحليلات...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\" dir=\"rtl\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center\">\n              <Link href=\"/dashboard\" className=\"text-blue-600 hover:text-blue-800 ml-4\">\n                ← العودة للوحة الرئيسية\n              </Link>\n              <h1 className=\"text-xl font-semibold text-gray-900\">\n                📈 التحليلات والإحصائيات\n              </h1>\n            </div>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <select\n                value={timeRange}\n                onChange={(e) => setTimeRange(e.target.value)}\n                className=\"px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"7\">آخر 7 أيام</option>\n                <option value=\"30\">آخر 30 يوم</option>\n                <option value=\"90\">آخر 3 أشهر</option>\n                <option value=\"365\">آخر سنة</option>\n              </select>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8\">\n        {/* Summary Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">🕌</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">متوسط العبادات</p>\n                <p className=\"text-2xl font-bold text-blue-600\">\n                  {Math.round(data.monthlyStats.avgWorshipPercentage)}%\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">📚</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">إجمالي ساعات التعلم</p>\n                <p className=\"text-2xl font-bold text-green-600\">\n                  {Math.round(data.monthlyStats.totalLearningHours)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">💰</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">إجمالي الإيرادات</p>\n                <p className=\"text-2xl font-bold text-yellow-600\">\n                  ${Math.round(data.monthlyStats.totalRevenue)}\n                </p>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <span className=\"text-2xl\">📊</span>\n              </div>\n              <div className=\"mr-4\">\n                <p className=\"text-sm font-medium text-gray-600\">صافي الربح</p>\n                <p className=\"text-2xl font-bold text-purple-600\">\n                  ${Math.round(data.monthlyStats.totalProfit)}\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Charts - Simplified Tables */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Worship Trend */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">اتجاه العبادات</h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">التاريخ</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">النسبة %</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {data.worshipTrend.slice(0, 5).map((item, index) => (\n                    <tr key={index}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{item.date}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{item.worship_percentage}%</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Learning Hours */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">ساعات التعلم اليومية</h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">التاريخ</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Laravel</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Node.js</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Next.js</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Flutter</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {data.learningTrend.slice(0, 5).map((item, index) => (\n                    <tr key={index}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{item.date}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-blue-600\">{item.Laravel}h</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-green-600\">{item['Node.js']}h</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-yellow-600\">{item['Next.js']}h</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-purple-600\">{item.Flutter}h</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Revenue Trend */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">اتجاه الإيرادات</h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">التاريخ</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">إجمالي الإيرادات</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">صافي الربح</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {data.revenueTrend.slice(0, 5).map((item, index) => (\n                    <tr key={index}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{item.date}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-green-600\">${item.total_revenue}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-blue-600\">${item.net_profit}</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n\n          {/* Project Progress */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">تقدم المشاريع</h3>\n            <div className=\"overflow-x-auto\">\n              <table className=\"min-w-full divide-y divide-gray-200\">\n                <thead className=\"bg-gray-50\">\n                  <tr>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">التاريخ</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Dash-sy</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Syriana Store</th>\n                    <th className=\"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider\">Booking-Sy</th>\n                  </tr>\n                </thead>\n                <tbody className=\"bg-white divide-y divide-gray-200\">\n                  {data.projectProgress.slice(0, 5).map((item, index) => (\n                    <tr key={index}>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">{item.date}</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-blue-600\">{item['Dash-sy']}%</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-green-600\">{item['Syriana Store']}%</td>\n                      <td className=\"px-6 py-4 whitespace-nowrap text-sm text-yellow-600\">{item['Booking-Sy']}%</td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n          </div>\n        </div>\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAC9C,cAAc,EAAE;QAChB,eAAe,EAAE;QACjB,cAAc,EAAE;QAChB,iBAAiB,EAAE;QACnB,cAAc,CAAC;IACjB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO;;IACxD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;kCAAG;QAAC;QAAM;KAAU;IAEpB,MAAM,YAAY;QAChB,MAAM,YAAW,MAAM,CAAA,GAAA,yHAAA,CAAA,cAAW,AAAD;QACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,UAAS,IAAI,CAAC,OAAO;QACtD,IAAI,CAAC,MAAM;YACT,OAAO,IAAI,CAAC;QACd,OAAO;YACL,QAAQ;QACV;QACA,WAAW;IACb;IAEA,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,UAAU,IAAI;YACpB,MAAM,YAAY,IAAI;YACtB,UAAU,OAAO,CAAC,QAAQ,OAAO,KAAK,SAAS;YAE/C,MAAM,eAAe,UAAU,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC1D,MAAM,aAAa,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAEtD,qBAAqB;YACrB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,4BACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,cACZ,GAAG,CAAC,QAAQ,YACZ,KAAK,CAAC;YAET,sBAAsB;YACtB,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,oBACL,MAAM,CAAC,0FACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,cACZ,GAAG,CAAC,QAAQ,YACZ,KAAK,CAAC;YAET,qBAAqB;YACrB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,mCACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,cACZ,GAAG,CAAC,QAAQ,YACZ,KAAK,CAAC;YAET,wBAAwB;YACxB,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,SACjC,IAAI,CAAC,mBACL,MAAM,CAAC,6DACP,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,GAAG,CAAC,QAAQ,cACZ,GAAG,CAAC,QAAQ,YACZ,KAAK,CAAC;YAET,QAAQ;gBACN,cAAc,eAAe,EAAE;gBAC/B,eAAe,cAAc,IAAI,CAAA,OAAQ,CAAC;wBACxC,GAAG,IAAI;wBACP,SAAS,KAAK,YAAY;wBAC1B,WAAW,KAAK,WAAW;wBAC3B,WAAW,KAAK,WAAW;wBAC3B,SAAS,KAAK,YAAY;wBAC1B,UAAU,KAAK,aAAa;oBAC9B,CAAC,MAAM,EAAE;gBACT,cAAc,eAAe,EAAE;gBAC/B,iBAAiB,aAAa,IAAI,CAAA,OAAQ,CAAC;wBACzC,GAAG,IAAI;wBACP,WAAW,KAAK,eAAe;wBAC/B,iBAAiB,KAAK,gBAAgB;wBACtC,cAAc,KAAK,gBAAgB;oBACrC,CAAC,MAAM,EAAE;gBACT,cAAc;oBACZ,kBAAkB,aAAa,UAAU;oBACzC,sBAAsB,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,kBAAkB,EAAE,KAAK,CAAC,aAAa,UAAU,CAAC,KAAK;oBAC3H,oBAAoB,cAAc,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE,MAAM;oBACtF,cAAc,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,aAAa,EAAE,MAAM;oBACjF,aAAa,aAAa,OAAO,CAAC,KAAK,OAAS,MAAM,KAAK,UAAU,EAAE,MAAM;gBAC/E;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QAAC;QAAW;QAAW;QAAW;QAAW;QAAW;KAAU;IAEjF,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,6LAAC;QAAI,WAAU;QAA0B,KAAI;;0BAE3C,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAa,WAAU;kDAAyC;;;;;;kDAG3E,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;;;;;;;0CAItD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oCAC5C,WAAU;;sDAEV,6LAAC;4CAAO,OAAM;sDAAI;;;;;;sDAClB,6LAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,6LAAC;4CAAO,OAAM;sDAAK;;;;;;sDACnB,6LAAC;4CAAO,OAAM;sDAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,6LAAC;gBAAK,WAAU;;kCAEd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;;wDACV,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,oBAAoB;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;0CAM5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DACV,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;0CAMxD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;;wDAAqC;wDAC9C,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAMnD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW;;;;;;;;;;;sDAE7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAoC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;;wDAAqC;wDAC9C,KAAK,KAAK,CAAC,KAAK,YAAY,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDAAM,WAAU;8DACf,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;;;;;;;;;;;;8DAGpG,6LAAC;oDAAM,WAAU;8DACd,KAAK,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACxC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqD,KAAK,IAAI;;;;;;8EAC5E,6LAAC;oEAAG,WAAU;;wEAAqD,KAAK,kBAAkB;wEAAC;;;;;;;;2DAFpF;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAWnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDAAM,WAAU;8DACf,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;;;;;;;;;;;;8DAGpG,6LAAC;oDAAM,WAAU;8DACd,KAAK,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACzC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqD,KAAK,IAAI;;;;;;8EAC5E,6LAAC;oEAAG,WAAU;;wEAAqD,KAAK,OAAO;wEAAC;;;;;;;8EAChF,6LAAC;oEAAG,WAAU;;wEAAsD,IAAI,CAAC,UAAU;wEAAC;;;;;;;8EACpF,6LAAC;oEAAG,WAAU;;wEAAuD,IAAI,CAAC,UAAU;wEAAC;;;;;;;8EACrF,6LAAC;oEAAG,WAAU;;wEAAuD,KAAK,OAAO;wEAAC;;;;;;;;2DAL3E;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAcnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDAAM,WAAU;8DACf,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;;;;;;;;;;;;8DAGpG,6LAAC;oDAAM,WAAU;8DACd,KAAK,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACxC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqD,KAAK,IAAI;;;;;;8EAC5E,6LAAC;oEAAG,WAAU;;wEAAqD;wEAAE,KAAK,aAAa;;;;;;;8EACvF,6LAAC;oEAAG,WAAU;;wEAAoD;wEAAE,KAAK,UAAU;;;;;;;;2DAH5E;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAM,WAAU;;8DACf,6LAAC;oDAAM,WAAU;8DACf,cAAA,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;0EAChG,6LAAC;gEAAG,WAAU;0EAAkF;;;;;;;;;;;;;;;;;8DAGpG,6LAAC;oDAAM,WAAU;8DACd,KAAK,eAAe,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC3C,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAqD,KAAK,IAAI;;;;;;8EAC5E,6LAAC;oEAAG,WAAU;;wEAAqD,IAAI,CAAC,UAAU;wEAAC;;;;;;;8EACnF,6LAAC;oEAAG,WAAU;;wEAAsD,IAAI,CAAC,gBAAgB;wEAAC;;;;;;;8EAC1F,6LAAC;oEAAG,WAAU;;wEAAuD,IAAI,CAAC,aAAa;wEAAC;;;;;;;;2DAJjF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAe7B;GApUwB;;QAWP,qIAAA,CAAA,YAAS;;;KAXF", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1229, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/shared/lib/router/utils/querystring.ts"], "sourcesContent": ["import type { ParsedUrlQuery } from 'querystring'\n\nexport function searchParamsToUrlQuery(\n  searchParams: URLSearchParams\n): ParsedUrlQuery {\n  const query: ParsedUrlQuery = {}\n  for (const [key, value] of searchParams.entries()) {\n    const existing = query[key]\n    if (typeof existing === 'undefined') {\n      query[key] = value\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      query[key] = [existing, value]\n    }\n  }\n  return query\n}\n\nfunction stringifyUrlQueryParam(param: unknown): string {\n  if (typeof param === 'string') {\n    return param\n  }\n\n  if (\n    (typeof param === 'number' && !isNaN(param)) ||\n    typeof param === 'boolean'\n  ) {\n    return String(param)\n  } else {\n    return ''\n  }\n}\n\nexport function urlQueryToSearchParams(query: ParsedUrlQuery): URLSearchParams {\n  const searchParams = new URLSearchParams()\n  for (const [key, value] of Object.entries(query)) {\n    if (Array.isArray(value)) {\n      for (const item of value) {\n        searchParams.append(key, stringifyUrlQueryParam(item))\n      }\n    } else {\n      searchParams.set(key, stringifyUrlQueryParam(value))\n    }\n  }\n  return searchParams\n}\n\nexport function assign(\n  target: URLSearchParams,\n  ...searchParamsList: URLSearchParams[]\n): URLSearchParams {\n  for (const searchParams of searchParamsList) {\n    for (const key of searchParams.keys()) {\n      target.delete(key)\n    }\n\n    for (const [key, value] of searchParams.entries()) {\n      target.append(key, value)\n    }\n  }\n\n  return target\n}\n"], "names": ["assign", "searchParamsToUrlQuery", "urlQueryToSearchParams", "searchParams", "query", "key", "value", "entries", "existing", "Array", "isArray", "push", "stringifyUrlQueryParam", "param", "isNaN", "String", "URLSearchParams", "Object", "item", "append", "set", "target", "searchParamsList", "keys", "delete"], "mappings": ";;;;;;;;;;;;;;;;IAgDgBA,MAAM,EAAA;eAANA;;IA9CAC,sBAAsB,EAAA;eAAtBA;;IAgCAC,sBAAsB,EAAA;eAAtBA;;;AAhCT,SAASD,uBACdE,YAA6B;IAE7B,MAAMC,QAAwB,CAAC;IAC/B,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;QACjD,MAAMC,WAAWJ,KAAK,CAACC,IAAI;QAC3B,IAAI,OAAOG,aAAa,aAAa;YACnCJ,KAAK,CAACC,IAAI,GAAGC;QACf,OAAO,IAAIG,MAAMC,OAAO,CAACF,WAAW;YAClCA,SAASG,IAAI,CAACL;QAChB,OAAO;YACLF,KAAK,CAACC,IAAI,GAAG;gBAACG;gBAAUF;aAAM;QAChC;IACF;IACA,OAAOF;AACT;AAEA,SAASQ,uBAAuBC,KAAc;IAC5C,IAAI,OAAOA,UAAU,UAAU;QAC7B,OAAOA;IACT;IAEA,IACG,OAAOA,UAAU,YAAY,CAACC,MAAMD,UACrC,OAAOA,UAAU,WACjB;QACA,OAAOE,OAAOF;IAChB,OAAO;QACL,OAAO;IACT;AACF;AAEO,SAASX,uBAAuBE,KAAqB;IAC1D,MAAMD,eAAe,IAAIa;IACzB,KAAK,MAAM,CAACX,KAAKC,MAAM,IAAIW,OAAOV,OAAO,CAACH,OAAQ;QAChD,IAAIK,MAAMC,OAAO,CAACJ,QAAQ;YACxB,KAAK,MAAMY,QAAQZ,MAAO;gBACxBH,aAAagB,MAAM,CAACd,KAAKO,uBAAuBM;YAClD;QACF,OAAO;YACLf,aAAaiB,GAAG,CAACf,KAAKO,uBAAuBN;QAC/C;IACF;IACA,OAAOH;AACT;AAEO,SAASH,OACdqB,MAAuB;IACvB,IAAA,IAAA,OAAA,UAAA,MAAA,EAAGC,mBAAH,IAAA,MAAA,OAAA,IAAA,OAAA,IAAA,IAAA,OAAA,GAAA,OAAA,MAAA,OAAA;QAAGA,gBAAAA,CAAH,OAAA,EAAA,GAAA,SAAA,CAAA,KAAsC;;IAEtC,KAAK,MAAMnB,gBAAgBmB,iBAAkB;QAC3C,KAAK,MAAMjB,OAAOF,aAAaoB,IAAI,GAAI;YACrCF,OAAOG,MAAM,CAACnB;QAChB;QAEA,KAAK,MAAM,CAACA,KAAKC,MAAM,IAAIH,aAAaI,OAAO,GAAI;YACjDc,OAAOF,MAAM,CAACd,KAAKC;QACrB;IACF;IAEA,OAAOe;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/shared/lib/router/utils/format-url.ts"], "sourcesContent": ["// Format function modified from nodejs\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\nimport type { UrlObject } from 'url'\nimport type { ParsedUrlQuery } from 'querystring'\nimport * as querystring from './querystring'\n\nconst slashedProtocols = /https?|ftp|gopher|file/\n\nexport function formatUrl(urlObj: UrlObject) {\n  let { auth, hostname } = urlObj\n  let protocol = urlObj.protocol || ''\n  let pathname = urlObj.pathname || ''\n  let hash = urlObj.hash || ''\n  let query = urlObj.query || ''\n  let host: string | false = false\n\n  auth = auth ? encodeURIComponent(auth).replace(/%3A/i, ':') + '@' : ''\n\n  if (urlObj.host) {\n    host = auth + urlObj.host\n  } else if (hostname) {\n    host = auth + (~hostname.indexOf(':') ? `[${hostname}]` : hostname)\n    if (urlObj.port) {\n      host += ':' + urlObj.port\n    }\n  }\n\n  if (query && typeof query === 'object') {\n    query = String(querystring.urlQueryToSearchParams(query as ParsedUrlQuery))\n  }\n\n  let search = urlObj.search || (query && `?${query}`) || ''\n\n  if (protocol && !protocol.endsWith(':')) protocol += ':'\n\n  if (\n    urlObj.slashes ||\n    ((!protocol || slashedProtocols.test(protocol)) && host !== false)\n  ) {\n    host = '//' + (host || '')\n    if (pathname && pathname[0] !== '/') pathname = '/' + pathname\n  } else if (!host) {\n    host = ''\n  }\n\n  if (hash && hash[0] !== '#') hash = '#' + hash\n  if (search && search[0] !== '?') search = '?' + search\n\n  pathname = pathname.replace(/[?#]/g, encodeURIComponent)\n  search = search.replace('#', '%23')\n\n  return `${protocol}${host}${pathname}${search}${hash}`\n}\n\nexport const urlObjectKeys = [\n  'auth',\n  'hash',\n  'host',\n  'hostname',\n  'href',\n  'path',\n  'pathname',\n  'port',\n  'protocol',\n  'query',\n  'search',\n  'slashes',\n]\n\nexport function formatWithValidation(url: UrlObject): string {\n  if (process.env.NODE_ENV === 'development') {\n    if (url !== null && typeof url === 'object') {\n      Object.keys(url).forEach((key) => {\n        if (!urlObjectKeys.includes(key)) {\n          console.warn(\n            `Unknown key passed via urlObject into url.format: ${key}`\n          )\n        }\n      })\n    }\n  }\n\n  return formatUrl(url)\n}\n"], "names": ["formatUrl", "formatWithValidation", "urlObjectKeys", "slashedProtocols", "url<PERSON>bj", "auth", "hostname", "protocol", "pathname", "hash", "query", "host", "encodeURIComponent", "replace", "indexOf", "port", "String", "querystring", "urlQueryToSearchParams", "search", "endsWith", "slashes", "test", "url", "process", "env", "NODE_ENV", "Object", "keys", "for<PERSON>ach", "key", "includes", "console", "warn"], "mappings": "AAAA,uCAAuC;AACvC,sDAAsD;AACtD,EAAE;AACF,0EAA0E;AAC1E,gEAAgE;AAChE,sEAAsE;AACtE,sEAAsE;AACtE,4EAA4E;AAC5E,qEAAqE;AACrE,wBAAwB;AACxB,EAAE;AACF,0EAA0E;AAC1E,yDAAyD;AACzD,EAAE;AACF,0EAA0E;AAC1E,6DAA6D;AAC7D,4EAA4E;AAC5E,2EAA2E;AAC3E,wEAAwE;AACxE,4EAA4E;AAC5E,yCAAyC;AAsEnCwB,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;IA9Df1B,SAAS,EAAA;eAATA;;IA6DAC,oBAAoB,EAAA;eAApBA;;IAfHC,aAAa,EAAA;eAAbA;;;;uEAlDgB;AAE7B,MAAMC,mBAAmB;AAElB,SAASH,UAAUI,MAAiB;IACzC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAE,GAAGF;IACzB,IAAIG,WAAWH,OAAOG,QAAQ,IAAI;IAClC,IAAIC,WAAWJ,OAAOI,QAAQ,IAAI;IAClC,IAAIC,OAAOL,OAAOK,IAAI,IAAI;IAC1B,IAAIC,QAAQN,OAAOM,KAAK,IAAI;IAC5B,IAAIC,OAAuB;IAE3BN,OAAOA,OAAOO,mBAAmBP,MAAMQ,OAAO,CAAC,QAAQ,OAAO,MAAM;IAEpE,IAAIT,OAAOO,IAAI,EAAE;QACfA,OAAON,OAAOD,OAAOO,IAAI;IAC3B,OAAO,IAAIL,UAAU;QACnBK,OAAON,OAAQ,CAAA,CAACC,SAASQ,OAAO,CAAC,OAAQ,MAAGR,WAAS,MAAKA,QAAO;QACjE,IAAIF,OAAOW,IAAI,EAAE;YACfJ,QAAQ,MAAMP,OAAOW,IAAI;QAC3B;IACF;IAEA,IAAIL,SAAS,OAAOA,UAAU,UAAU;QACtCA,QAAQM,OAAOC,aAAYC,sBAAsB,CAACR;IACpD;IAEA,IAAIS,SAASf,OAAOe,MAAM,IAAKT,SAAU,MAAGA,SAAY;IAExD,IAAIH,YAAY,CAACA,SAASa,QAAQ,CAAC,MAAMb,YAAY;IAErD,IACEH,OAAOiB,OAAO,IACZ,CAAA,CAACd,YAAYJ,iBAAiBmB,IAAI,CAACf,SAAQ,KAAMI,SAAS,OAC5D;QACAA,OAAO,OAAQA,CAAAA,QAAQ,EAAC;QACxB,IAAIH,YAAYA,QAAQ,CAAC,EAAE,KAAK,KAAKA,WAAW,MAAMA;IACxD,OAAO,IAAI,CAACG,MAAM;QAChBA,OAAO;IACT;IAEA,IAAIF,QAAQA,IAAI,CAAC,EAAE,KAAK,KAAKA,OAAO,MAAMA;IAC1C,IAAIU,UAAUA,MAAM,CAAC,EAAE,KAAK,KAAKA,SAAS,MAAMA;IAEhDX,WAAWA,SAASK,OAAO,CAAC,SAASD;IACrCO,SAASA,OAAON,OAAO,CAAC,KAAK;IAE7B,OAAQ,KAAEN,WAAWI,OAAOH,WAAWW,SAASV;AAClD;AAEO,MAAMP,gBAAgB;IAC3B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAASD,qBAAqBsB,GAAc;IACjD,wCAA4C;QAC1C,IAAIA,QAAQ,QAAQ,OAAOA,QAAQ,UAAU;YAC3CI,OAAOC,IAAI,CAACL,KAAKM,OAAO,CAAC,CAACC;gBACxB,IAAI,CAAC5B,cAAc6B,QAAQ,CAACD,MAAM;oBAChCE,QAAQC,IAAI,CACT,uDAAoDH;gBAEzD;YACF;QACF;IACF;IAEA,OAAO9B,UAAUuB;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/client/use-merged-ref.ts"], "sourcesContent": ["import { useCallback, useRef, type Ref } from 'react'\n\n// This is a compatibility hook to support React 18 and 19 refs.\n// In 19, a cleanup function from refs may be returned.\n// In 18, returning a cleanup function creates a warning.\n// Since we take userspace refs, we don't know ahead of time if a cleanup function will be returned.\n// This implements cleanup functions with the old behavior in 18.\n// We know refs are always called alternating with `null` and then `T`.\n// So a call with `null` means we need to call the previous cleanup functions.\nexport function useMergedRef<TElement>(\n  refA: Ref<TElement>,\n  refB: Ref<TElement>\n): Ref<TElement> {\n  const cleanupA = useRef<(() => void) | null>(null)\n  const cleanupB = useRef<(() => void) | null>(null)\n\n  // NOTE: In theory, we could skip the wrapping if only one of the refs is non-null.\n  // (this happens often if the user doesn't pass a ref to Link/Form/Image)\n  // But this can cause us to leak a cleanup-ref into user code (e.g. via `<Link legacyBehavior>`),\n  // and the user might pass that ref into ref-merging library that doesn't support cleanup refs\n  // (because it hasn't been updated for React 19)\n  // which can then cause things to blow up, because a cleanup-returning ref gets called with `null`.\n  // So in practice, it's safer to be defensive and always wrap the ref, even on React 19.\n  return useCallback(\n    (current: TElement | null): void => {\n      if (current === null) {\n        const cleanupFnA = cleanupA.current\n        if (cleanupFnA) {\n          cleanupA.current = null\n          cleanupFnA()\n        }\n        const cleanupFnB = cleanupB.current\n        if (cleanupFnB) {\n          cleanupB.current = null\n          cleanupFnB()\n        }\n      } else {\n        if (refA) {\n          cleanupA.current = applyRef(refA, current)\n        }\n        if (refB) {\n          cleanupB.current = applyRef(refB, current)\n        }\n      }\n    },\n    [refA, refB]\n  )\n}\n\nfunction applyRef<TElement>(\n  refA: NonNullable<Ref<TElement>>,\n  current: TElement\n) {\n  if (typeof refA === 'function') {\n    const cleanup = refA(current)\n    if (typeof cleanup === 'function') {\n      return cleanup\n    } else {\n      return () => refA(null)\n    }\n  } else {\n    refA.current = current\n    return () => {\n      refA.current = null\n    }\n  }\n}\n"], "names": ["useMergedRef", "refA", "refB", "cleanupA", "useRef", "cleanupB", "useCallback", "current", "cleanupFnA", "cleanupFnB", "applyRef", "cleanup"], "mappings": ";;;;+BASgBA,gBAAAA;;;eAAAA;;;uBAT8B;AASvC,SAASA,aACdC,IAAmB,EACnBC,IAAmB;IAEnB,MAAMC,WAAWC,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAC7C,MAAMC,WAAWD,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAE7C,mFAAmF;IACnF,yEAAyE;IACzE,iGAAiG;IACjG,8FAA8F;IAC9F,gDAAgD;IAChD,mGAAmG;IACnG,wFAAwF;IACxF,OAAOE,CAAAA,GAAAA,OAAAA,WAAW,EAChB,CAACC;QACC,IAAIA,YAAY,MAAM;YACpB,MAAMC,aAAaL,SAASI,OAAO;YACnC,IAAIC,YAAY;gBACdL,SAASI,OAAO,GAAG;gBACnBC;YACF;YACA,MAAMC,aAAaJ,SAASE,OAAO;YACnC,IAAIE,YAAY;gBACdJ,SAASE,OAAO,GAAG;gBACnBE;YACF;QACF,OAAO;YACL,IAAIR,MAAM;gBACRE,SAASI,OAAO,GAAGG,SAAST,MAAMM;YACpC;YACA,IAAIL,MAAM;gBACRG,SAASE,OAAO,GAAGG,SAASR,MAAMK;YACpC;QACF;IACF,GACA;QAACN;QAAMC;KAAK;AAEhB;AAEA,SAASQ,SACPT,IAAgC,EAChCM,OAAiB;IAEjB,IAAI,OAAON,SAAS,YAAY;QAC9B,MAAMU,UAAUV,KAAKM;QACrB,IAAI,OAAOI,YAAY,YAAY;YACjC,OAAOA;QACT,OAAO;YACL,OAAO,IAAMV,KAAK;QACpB;IACF,OAAO;QACLA,KAAKM,OAAO,GAAGA;QACf,OAAO;YACLN,KAAKM,OAAO,GAAG;QACjB;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/shared/lib/utils.ts"], "sourcesContent": ["import type { HtmlProps } from './html-context.shared-runtime'\nimport type { ComponentType, JSX } from 'react'\nimport type { DomainLocale } from '../../server/config'\nimport type { Env } from '@next/env'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { NextRouter } from './router/router'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { PreviewData } from '../../types'\nimport type { COMPILER_NAMES } from './constants'\nimport type fs from 'fs'\n\nexport type NextComponentType<\n  Context extends BaseContext = NextPageContext,\n  InitialProps = {},\n  Props = {},\n> = ComponentType<Props> & {\n  /**\n   * Used for initial page load data population. Data returned from `getInitialProps` is serialized when server rendered.\n   * Make sure to return plain `Object` without using `Date`, `Map`, `Set`.\n   * @param context Context of `page`\n   */\n  getInitialProps?(context: Context): InitialProps | Promise<InitialProps>\n}\n\nexport type DocumentType = NextComponentType<\n  DocumentContext,\n  DocumentInitialProps,\n  DocumentProps\n>\n\nexport type AppType<P = {}> = NextComponentType<\n  AppContextType,\n  P,\n  AppPropsType<any, P>\n>\n\nexport type AppTreeType = ComponentType<\n  AppInitialProps & { [name: string]: any }\n>\n\n/**\n * Web vitals provided to _app.reportWebVitals by Core Web Vitals plugin developed by Google Chrome team.\n * https://nextjs.org/blog/next-9-4#integrated-web-vitals-reporting\n */\nexport const WEB_VITALS = ['CLS', 'FCP', 'FID', 'INP', 'LCP', 'TTFB'] as const\nexport type NextWebVitalsMetric = {\n  id: string\n  startTime: number\n  value: number\n  attribution?: { [key: string]: unknown }\n} & (\n  | {\n      label: 'web-vital'\n      name: (typeof WEB_VITALS)[number]\n    }\n  | {\n      label: 'custom'\n      name:\n        | 'Next.js-hydration'\n        | 'Next.js-route-change-to-render'\n        | 'Next.js-render'\n    }\n)\n\nexport type Enhancer<C> = (Component: C) => C\n\nexport type ComponentsEnhancer =\n  | {\n      enhanceApp?: Enhancer<AppType>\n      enhanceComponent?: Enhancer<NextComponentType>\n    }\n  | Enhancer<NextComponentType>\n\nexport type RenderPageResult = {\n  html: string\n  head?: Array<JSX.Element | null>\n}\n\nexport type RenderPage = (\n  options?: ComponentsEnhancer\n) => DocumentInitialProps | Promise<DocumentInitialProps>\n\nexport type BaseContext = {\n  res?: ServerResponse\n  [k: string]: any\n}\n\nexport type NEXT_DATA = {\n  props: Record<string, any>\n  page: string\n  query: ParsedUrlQuery\n  buildId: string\n  assetPrefix?: string\n  runtimeConfig?: { [key: string]: any }\n  nextExport?: boolean\n  autoExport?: boolean\n  isFallback?: boolean\n  isExperimentalCompile?: boolean\n  dynamicIds?: (string | number)[]\n  err?: Error & {\n    statusCode?: number\n    source?: typeof COMPILER_NAMES.server | typeof COMPILER_NAMES.edgeServer\n  }\n  gsp?: boolean\n  gssp?: boolean\n  customServer?: boolean\n  gip?: boolean\n  appGip?: boolean\n  locale?: string\n  locales?: readonly string[]\n  defaultLocale?: string\n  domainLocales?: readonly DomainLocale[]\n  scriptLoader?: any[]\n  isPreview?: boolean\n  notFoundSrcPage?: string\n}\n\n/**\n * `Next` context\n */\nexport interface NextPageContext {\n  /**\n   * Error object if encountered during rendering\n   */\n  err?: (Error & { statusCode?: number }) | null\n  /**\n   * `HTTP` request object.\n   */\n  req?: IncomingMessage\n  /**\n   * `HTTP` response object.\n   */\n  res?: ServerResponse\n  /**\n   * Path section of `URL`.\n   */\n  pathname: string\n  /**\n   * Query string section of `URL` parsed as an object.\n   */\n  query: ParsedUrlQuery\n  /**\n   * `String` of the actual path including query.\n   */\n  asPath?: string\n  /**\n   * The currently active locale\n   */\n  locale?: string\n  /**\n   * All configured locales\n   */\n  locales?: readonly string[]\n  /**\n   * The configured default locale\n   */\n  defaultLocale?: string\n  /**\n   * `Component` the tree of the App to use if needing to render separately\n   */\n  AppTree: AppTreeType\n}\n\nexport type AppContextType<Router extends NextRouter = NextRouter> = {\n  Component: NextComponentType<NextPageContext>\n  AppTree: AppTreeType\n  ctx: NextPageContext\n  router: Router\n}\n\nexport type AppInitialProps<PageProps = any> = {\n  pageProps: PageProps\n}\n\nexport type AppPropsType<\n  Router extends NextRouter = NextRouter,\n  PageProps = {},\n> = AppInitialProps<PageProps> & {\n  Component: NextComponentType<NextPageContext, any, any>\n  router: Router\n  __N_SSG?: boolean\n  __N_SSP?: boolean\n}\n\nexport type DocumentContext = NextPageContext & {\n  renderPage: RenderPage\n  defaultGetInitialProps(\n    ctx: DocumentContext,\n    options?: { nonce?: string }\n  ): Promise<DocumentInitialProps>\n}\n\nexport type DocumentInitialProps = RenderPageResult & {\n  styles?: React.ReactElement[] | Iterable<React.ReactNode> | JSX.Element\n}\n\nexport type DocumentProps = DocumentInitialProps & HtmlProps\n\n/**\n * Next `API` route request\n */\nexport interface NextApiRequest extends IncomingMessage {\n  /**\n   * Object of `query` values from url\n   */\n  query: Partial<{\n    [key: string]: string | string[]\n  }>\n  /**\n   * Object of `cookies` from header\n   */\n  cookies: Partial<{\n    [key: string]: string\n  }>\n\n  body: any\n\n  env: Env\n\n  draftMode?: boolean\n\n  preview?: boolean\n  /**\n   * Preview data set on the request, if any\n   * */\n  previewData?: PreviewData\n}\n\n/**\n * Send body of response\n */\ntype Send<T> = (body: T) => void\n\n/**\n * Next `API` route response\n */\nexport type NextApiResponse<Data = any> = ServerResponse & {\n  /**\n   * Send data `any` data in response\n   */\n  send: Send<Data>\n  /**\n   * Send data `json` data in response\n   */\n  json: Send<Data>\n  status: (statusCode: number) => NextApiResponse<Data>\n  redirect(url: string): NextApiResponse<Data>\n  redirect(status: number, url: string): NextApiResponse<Data>\n\n  /**\n   * Set draft mode\n   */\n  setDraftMode: (options: { enable: boolean }) => NextApiResponse<Data>\n\n  /**\n   * Set preview data for Next.js' prerender mode\n   */\n  setPreviewData: (\n    data: object | string,\n    options?: {\n      /**\n       * Specifies the number (in seconds) for the preview session to last for.\n       * The given number will be converted to an integer by rounding down.\n       * By default, no maximum age is set and the preview session finishes\n       * when the client shuts down (browser is closed).\n       */\n      maxAge?: number\n      /**\n       * Specifies the path for the preview session to work under. By default,\n       * the path is considered the \"default path\", i.e., any pages under \"/\".\n       */\n      path?: string\n    }\n  ) => NextApiResponse<Data>\n\n  /**\n   * Clear preview data for Next.js' prerender mode\n   */\n  clearPreviewData: (options?: { path?: string }) => NextApiResponse<Data>\n\n  /**\n   * Revalidate a specific page and regenerate it using On-Demand Incremental\n   * Static Regeneration.\n   * The path should be an actual path, not a rewritten path. E.g. for\n   * \"/blog/[slug]\" this should be \"/blog/post-1\".\n   * @link https://nextjs.org/docs/app/building-your-application/data-fetching/incremental-static-regeneration#on-demand-revalidation-with-revalidatepath\n   */\n  revalidate: (\n    urlPath: string,\n    opts?: {\n      unstable_onlyGenerated?: boolean\n    }\n  ) => Promise<void>\n}\n\n/**\n * Next `API` route handler\n */\nexport type NextApiHandler<T = any> = (\n  req: NextApiRequest,\n  res: NextApiResponse<T>\n) => unknown | Promise<unknown>\n\n/**\n * Utils\n */\nexport function execOnce<T extends (...args: any[]) => ReturnType<T>>(\n  fn: T\n): T {\n  let used = false\n  let result: ReturnType<T>\n\n  return ((...args: any[]) => {\n    if (!used) {\n      used = true\n      result = fn(...args)\n    }\n    return result\n  }) as T\n}\n\n// Scheme: https://tools.ietf.org/html/rfc3986#section-3.1\n// Absolute URL: https://tools.ietf.org/html/rfc3986#section-4.3\nconst ABSOLUTE_URL_REGEX = /^[a-zA-Z][a-zA-Z\\d+\\-.]*?:/\nexport const isAbsoluteUrl = (url: string) => ABSOLUTE_URL_REGEX.test(url)\n\nexport function getLocationOrigin() {\n  const { protocol, hostname, port } = window.location\n  return `${protocol}//${hostname}${port ? ':' + port : ''}`\n}\n\nexport function getURL() {\n  const { href } = window.location\n  const origin = getLocationOrigin()\n  return href.substring(origin.length)\n}\n\nexport function getDisplayName<P>(Component: ComponentType<P>) {\n  return typeof Component === 'string'\n    ? Component\n    : Component.displayName || Component.name || 'Unknown'\n}\n\nexport function isResSent(res: ServerResponse) {\n  return res.finished || res.headersSent\n}\n\nexport function normalizeRepeatedSlashes(url: string) {\n  const urlParts = url.split('?')\n  const urlNoQuery = urlParts[0]\n\n  return (\n    urlNoQuery\n      // first we replace any non-encoded backslashes with forward\n      // then normalize repeated forward slashes\n      .replace(/\\\\/g, '/')\n      .replace(/\\/\\/+/g, '/') +\n    (urlParts[1] ? `?${urlParts.slice(1).join('?')}` : '')\n  )\n}\n\nexport async function loadGetInitialProps<\n  C extends BaseContext,\n  IP = {},\n  P = {},\n>(App: NextComponentType<C, IP, P>, ctx: C): Promise<IP> {\n  if (process.env.NODE_ENV !== 'production') {\n    if (App.prototype?.getInitialProps) {\n      const message = `\"${getDisplayName(\n        App\n      )}.getInitialProps()\" is defined as an instance method - visit https://nextjs.org/docs/messages/get-initial-props-as-an-instance-method for more information.`\n      throw new Error(message)\n    }\n  }\n  // when called from _app `ctx` is nested in `ctx`\n  const res = ctx.res || (ctx.ctx && ctx.ctx.res)\n\n  if (!App.getInitialProps) {\n    if (ctx.ctx && ctx.Component) {\n      // @ts-ignore pageProps default\n      return {\n        pageProps: await loadGetInitialProps(ctx.Component, ctx.ctx),\n      }\n    }\n    return {} as IP\n  }\n\n  const props = await App.getInitialProps(ctx)\n\n  if (res && isResSent(res)) {\n    return props\n  }\n\n  if (!props) {\n    const message = `\"${getDisplayName(\n      App\n    )}.getInitialProps()\" should resolve to an object. But found \"${props}\" instead.`\n    throw new Error(message)\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (Object.keys(props).length === 0 && !ctx.ctx) {\n      console.warn(\n        `${getDisplayName(\n          App\n        )} returned an empty object from \\`getInitialProps\\`. This de-optimizes and prevents automatic static optimization. https://nextjs.org/docs/messages/empty-object-getInitialProps`\n      )\n    }\n  }\n\n  return props\n}\n\nexport const SP = typeof performance !== 'undefined'\nexport const ST =\n  SP &&\n  (['mark', 'measure', 'getEntriesByName'] as const).every(\n    (method) => typeof performance[method] === 'function'\n  )\n\nexport class DecodeError extends Error {}\nexport class NormalizeError extends Error {}\nexport class PageNotFoundError extends Error {\n  code: string\n\n  constructor(page: string) {\n    super()\n    this.code = 'ENOENT'\n    this.name = 'PageNotFoundError'\n    this.message = `Cannot find module for page: ${page}`\n  }\n}\n\nexport class MissingStaticPage extends Error {\n  constructor(page: string, message: string) {\n    super()\n    this.message = `Failed to load static file for page: ${page} ${message}`\n  }\n}\n\nexport class MiddlewareNotFoundError extends Error {\n  code: string\n  constructor() {\n    super()\n    this.code = 'ENOENT'\n    this.message = `Cannot find the middleware module`\n  }\n}\n\nexport interface CacheFs {\n  existsSync: typeof fs.existsSync\n  readFile: typeof fs.promises.readFile\n  readFileSync: typeof fs.readFileSync\n  writeFile(f: string, d: any): Promise<void>\n  mkdir(dir: string): Promise<void | string>\n  stat(f: string): Promise<{ mtime: Date }>\n}\n\nexport function stringifyError(error: Error) {\n  return JSON.stringify({ message: error.message, stack: error.stack })\n}\n"], "names": ["DecodeError", "MiddlewareNotFoundError", "MissingStaticPage", "NormalizeError", "PageNotFoundError", "SP", "ST", "WEB_VITALS", "execOnce", "getDisplayName", "getLocationOrigin", "getURL", "isAbsoluteUrl", "isResSent", "loadGetInitialProps", "normalizeRepeatedSlashes", "stringifyError", "fn", "used", "result", "args", "ABSOLUTE_URL_REGEX", "url", "test", "protocol", "hostname", "port", "window", "location", "href", "origin", "substring", "length", "Component", "displayName", "name", "res", "finished", "headersSent", "urlParts", "split", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "replace", "slice", "join", "App", "ctx", "process", "env", "NODE_ENV", "prototype", "getInitialProps", "message", "Error", "pageProps", "props", "Object", "keys", "console", "warn", "performance", "every", "method", "constructor", "page", "code", "error", "JSON", "stringify", "stack"], "mappings": "AA8WM+C,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsDlBjD,WAAW,EAAA;eAAXA;;IAoBAC,uBAAuB,EAAA;eAAvBA;;IAPAC,iBAAiB,EAAA;eAAjBA;;IAZAC,cAAc,EAAA;eAAdA;;IACAC,iBAAiB,EAAA;eAAjBA;;IATAC,EAAE,EAAA;eAAFA;;IACAC,EAAE,EAAA;eAAFA;;IAlXAC,UAAU,EAAA;eAAVA;;IAsQGC,QAAQ,EAAA;eAARA;;IA+BAC,cAAc,EAAA;eAAdA;;IAXAC,iBAAiB,EAAA;eAAjBA;;IAKAC,MAAM,EAAA;eAANA;;IAPHC,aAAa,EAAA;eAAbA;;IAmBGC,SAAS,EAAA;eAATA;;IAkBMC,mBAAmB,EAAA;eAAnBA;;IAdNC,wBAAwB,EAAA;eAAxBA;;IA+GAC,cAAc,EAAA;eAAdA;;;AA9ZT,MAAMT,aAAa;IAAC;IAAO;IAAO;IAAO;IAAO;IAAO;CAAO;AAsQ9D,SAASC,SACdS,EAAK;IAEL,IAAIC,OAAO;IACX,IAAIC;IAEJ,OAAQ;yCAAIC,OAAAA,IAAAA,MAAAA,OAAAA,OAAAA,GAAAA,OAAAA,MAAAA,OAAAA;YAAAA,IAAAA,CAAAA,KAAAA,GAAAA,SAAAA,CAAAA,KAAAA;;QACV,IAAI,CAACF,MAAM;YACTA,OAAO;YACPC,SAASF,MAAMG;QACjB;QACA,OAAOD;IACT;AACF;AAEA,0DAA0D;AAC1D,gEAAgE;AAChE,MAAME,qBAAqB;AACpB,MAAMT,gBAAgB,CAACU,MAAgBD,mBAAmBE,IAAI,CAACD;AAE/D,SAASZ;IACd,MAAM,EAAEc,QAAQ,EAAEC,QAAQ,EAAEC,IAAI,EAAE,GAAGC,OAAOC,QAAQ;IACpD,OAAUJ,WAAS,OAAIC,WAAWC,CAAAA,OAAO,MAAMA,OAAO,EAAC;AACzD;AAEO,SAASf;IACd,MAAM,EAAEkB,IAAI,EAAE,GAAGF,OAAOC,QAAQ;IAChC,MAAME,SAASpB;IACf,OAAOmB,KAAKE,SAAS,CAACD,OAAOE,MAAM;AACrC;AAEO,SAASvB,eAAkBwB,SAA2B;IAC3D,OAAO,OAAOA,cAAc,WACxBA,YACAA,UAAUC,WAAW,IAAID,UAAUE,IAAI,IAAI;AACjD;AAEO,SAAStB,UAAUuB,GAAmB;IAC3C,OAAOA,IAAIC,QAAQ,IAAID,IAAIE,WAAW;AACxC;AAEO,SAASvB,yBAAyBO,GAAW;IAClD,MAAMiB,WAAWjB,IAAIkB,KAAK,CAAC;IAC3B,MAAMC,aAAaF,QAAQ,CAAC,EAAE;IAE9B,OACEE,WACE,4DAA4D;IAC5D,0CAA0C;KACzCC,OAAO,CAAC,OAAO,KACfA,OAAO,CAAC,UAAU,OACpBH,CAAAA,QAAQ,CAAC,EAAE,GAAI,MAAGA,SAASI,KAAK,CAAC,GAAGC,IAAI,CAAC,OAAS,EAAC;AAExD;AAEO,eAAe9B,oBAIpB+B,GAAgC,EAAEC,GAAM;IACxC,wCAA2C;YACrCD;QAAJ,IAAA,CAAIA,iBAAAA,IAAIK,SAAS,KAAA,OAAA,KAAA,IAAbL,eAAeM,eAAe,EAAE;YAClC,MAAMC,UAAW,MAAG3C,eAClBoC,OACA;YACF,MAAM,OAAA,cAAkB,CAAlB,IAAIQ,MAAMD,UAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAiB;QACzB;IACF;IACA,iDAAiD;IACjD,MAAMhB,MAAMU,IAAIV,GAAG,IAAKU,IAAIA,GAAG,IAAIA,IAAIA,GAAG,CAACV,GAAG;IAE9C,IAAI,CAACS,IAAIM,eAAe,EAAE;QACxB,IAAIL,IAAIA,GAAG,IAAIA,IAAIb,SAAS,EAAE;YAC5B,+BAA+B;YAC/B,OAAO;gBACLqB,WAAW,MAAMxC,oBAAoBgC,IAAIb,SAAS,EAAEa,IAAIA,GAAG;YAC7D;QACF;QACA,OAAO,CAAC;IACV;IAEA,MAAMS,QAAQ,MAAMV,IAAIM,eAAe,CAACL;IAExC,IAAIV,OAAOvB,UAAUuB,MAAM;QACzB,OAAOmB;IACT;IAEA,IAAI,CAACA,OAAO;QACV,MAAMH,UAAW,MAAG3C,eAClBoC,OACA,iEAA8DU,QAAM;QACtE,MAAM,OAAA,cAAkB,CAAlB,IAAIF,MAAMD,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;IACzB;IAEA,IAAIL,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAIO,OAAOC,IAAI,CAACF,OAAOvB,MAAM,KAAK,KAAK,CAACc,IAAIA,GAAG,EAAE;YAC/CY,QAAQC,IAAI,CACT,KAAElD,eACDoC,OACA;QAEN;IACF;IAEA,OAAOU;AACT;AAEO,MAAMlD,KAAK,OAAOuD,gBAAgB;AAClC,MAAMtD,KACXD,MACC;IAAC;IAAQ;IAAW;CAAmB,CAAWwD,KAAK,CACtD,CAACC,SAAW,OAAOF,WAAW,CAACE,OAAO,KAAK;AAGxC,MAAM9D,oBAAoBqD;AAAO;AACjC,MAAMlD,uBAAuBkD;AAAO;AACpC,MAAMjD,0BAA0BiD;IAGrCU,YAAYC,IAAY,CAAE;QACxB,KAAK;QACL,IAAI,CAACC,IAAI,GAAG;QACZ,IAAI,CAAC9B,IAAI,GAAG;QACZ,IAAI,CAACiB,OAAO,GAAI,kCAA+BY;IACjD;AACF;AAEO,MAAM9D,0BAA0BmD;IACrCU,YAAYC,IAAY,EAAEZ,OAAe,CAAE;QACzC,KAAK;QACL,IAAI,CAACA,OAAO,GAAI,0CAAuCY,OAAK,MAAGZ;IACjE;AACF;AAEO,MAAMnD,gCAAgCoD;IAE3CU,aAAc;QACZ,KAAK;QACL,IAAI,CAACE,IAAI,GAAG;QACZ,IAAI,CAACb,OAAO,GAAI;IAClB;AACF;AAWO,SAASpC,eAAekD,KAAY;IACzC,OAAOC,KAAKC,SAAS,CAAC;QAAEhB,SAASc,MAAMd,OAAO;QAAEiB,OAAOH,MAAMG,KAAK;IAAC;AACrE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/shared/lib/router/utils/is-local-url.ts"], "sourcesContent": ["import { isAbsoluteUrl, getLocationOrigin } from '../../utils'\nimport { hasBasePath } from '../../../../client/has-base-path'\n\n/**\n * Detects whether a given url is routable by the Next.js router (browser only).\n */\nexport function isLocalURL(url: string): boolean {\n  // prevent a hydration mismatch on href for url with anchor refs\n  if (!isAbsoluteUrl(url)) return true\n  try {\n    // absolute urls can be local if they are on the same origin\n    const locationOrigin = getLocationOrigin()\n    const resolved = new URL(url, locationOrigin)\n    return resolved.origin === locationOrigin && hasBasePath(resolved.pathname)\n  } catch (_) {\n    return false\n  }\n}\n"], "names": ["isLocalURL", "url", "isAbsoluteUrl", "locationOrigin", "getLocationOrigin", "resolved", "URL", "origin", "has<PERSON>ase<PERSON><PERSON>", "pathname", "_"], "mappings": ";;;;+BAMgBA,cAAAA;;;eAAAA;;;uBANiC;6BACrB;AAKrB,SAASA,WAAWC,GAAW;IACpC,gEAAgE;IAChE,IAAI,CAACC,CAAAA,GAAAA,OAAAA,aAAa,EAACD,MAAM,OAAO;IAChC,IAAI;QACF,4DAA4D;QAC5D,MAAME,iBAAiBC,CAAAA,GAAAA,OAAAA,iBAAiB;QACxC,MAAMC,WAAW,IAAIC,IAAIL,KAAKE;QAC9B,OAAOE,SAASE,MAAM,KAAKJ,kBAAkBK,CAAAA,GAAAA,aAAAA,WAAW,EAACH,SAASI,QAAQ;IAC5E,EAAE,OAAOC,GAAG;QACV,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/shared/lib/utils/error-once.ts"], "sourcesContent": ["let errorOnce = (_: string) => {}\nif (process.env.NODE_ENV !== 'production') {\n  const errors = new Set<string>()\n  errorOnce = (msg: string) => {\n    if (!errors.has(msg)) {\n      console.error(msg)\n    }\n    errors.add(msg)\n  }\n}\n\nexport { errorOnce }\n"], "names": ["errorOnce", "_", "process", "env", "NODE_ENV", "errors", "Set", "msg", "has", "console", "error", "add"], "mappings": "AACIE,QAAQC,GAAG,CAACC,QAAQ,KAAK;;;;;+BAUpBJ,aAAAA;;;eAAAA;;;AAXT,IAAIA,YAAY,CAACC,KAAe;AAChC,wCAA2C;IACzC,MAAMI,SAAS,IAAIC;IACnBN,YAAY,CAACO;QACX,IAAI,CAACF,OAAOG,GAAG,CAACD,MAAM;YACpBE,QAAQC,KAAK,CAACH;QAChB;QACAF,OAAOM,GAAG,CAACJ;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1772, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `null` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  const navigate = () => {\n    if (onNavigate) {\n      let isDefaultPrevented = false\n\n      onNavigate({\n        preventDefault: () => {\n          isDefaultPrevented = true\n        },\n      })\n\n      if (isDefaultPrevented) {\n        return\n      }\n    }\n\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  }\n\n  React.startTransition(navigate)\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null ? PrefetchKind.AUTO : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'prefetch' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "navigate", "isDefaultPrevented", "dispatchNavigateAction", "current", "React", "startTransition", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "AAkXMuE,QAAQC,GAAG,CAACC,QAAQ,KAAK;AAlX/B;;;;;;;;;;;;;;;;IAmTA;;;;;;;;;CASC,GACD,OAyZC,EAAA;eAzZuBzE;;IA+ZXC,aAAa,EAAA;eAAbA;;;;;iEA1tB2D;2BAE9C;+CACO;oCACJ;8BACA;uBACC;6BACF;0BACH;uBASlB;4BACoB;mCACY;2BACb;AA0M1B,SAASC,gBAAgBC,KAAuB;IAC9C,MAAMC,cAAcD,MAAME,aAAa;IACvC,MAAMC,SAASF,YAAYG,YAAY,CAAC;IACxC,OACGD,UAAUA,WAAW,WACtBH,MAAMK,OAAO,IACbL,MAAMM,OAAO,IACbN,MAAMO,QAAQ,IACdP,MAAMQ,MAAM,IAAI,6BAA6B;IAC5CR,MAAMS,WAAW,IAAIT,MAAMS,WAAW,CAACC,KAAK,KAAK;AAEtD;AAEA,SAASC,YACPC,CAAmB,EACnBC,IAAY,EACZC,EAAU,EACVC,eAAqD,EACrDC,OAAiB,EACjBC,MAAgB,EAChBC,UAAmC;IAEnC,MAAM,EAAEC,QAAQ,EAAE,GAAGP,EAAEV,aAAa;IAEpC,kDAAkD;IAClD,MAAMkB,mBAAmBD,SAASE,WAAW,OAAO;IAEpD,IACGD,oBAAoBrB,gBAAgBa,MACrCA,EAAEV,aAAa,CAACoB,YAAY,CAAC,aAC7B;QACA,8CAA8C;QAC9C;IACF;IAEA,IAAI,CAACC,CAAAA,GAAAA,YAAAA,UAAU,EAACV,OAAO;QACrB,IAAIG,SAAS;YACX,8DAA8D;YAC9D,+BAA+B;YAC/BJ,EAAEY,cAAc;YAChBC,SAAST,OAAO,CAACH;QACnB;QAEA,8CAA8C;QAC9C;IACF;IAEAD,EAAEY,cAAc;IAEhB,MAAME,WAAW;QACf,IAAIR,YAAY;YACd,IAAIS,qBAAqB;YAEzBT,WAAW;gBACTM,gBAAgB;oBACdG,qBAAqB;gBACvB;YACF;YAEA,IAAIA,oBAAoB;gBACtB;YACF;QACF;QAEAC,CAAAA,GAAAA,mBAAAA,sBAAsB,EACpBd,MAAMD,MACNG,UAAU,YAAY,QACtBC,UAAAA,OAAAA,SAAU,MACVF,gBAAgBc,OAAO;IAE3B;IAEAC,OAAAA,OAAK,CAACC,eAAe,CAACL;AACxB;AAEA,SAASM,kBAAkBC,cAAkC;IAC3D,IAAI,OAAOA,mBAAmB,UAAU;QACtC,OAAOA;IACT;IAEA,OAAOC,CAAAA,GAAAA,WAAAA,SAAS,EAACD;AACnB;AAYe,SAASpC,cACtBsC,KAGC;IAED,MAAM,CAACC,YAAYC,wBAAwB,GAAGC,CAAAA,GAAAA,OAAAA,aAAa,EAACC,OAAAA,gBAAgB;IAE5E,IAAIC;IAEJ,MAAMzB,kBAAkB0B,CAAAA,GAAAA,OAAAA,MAAM,EAAsB;IAEpD,MAAM,EACJ5B,MAAM6B,QAAQ,EACd5B,IAAI6B,MAAM,EACVH,UAAUI,YAAY,EACtBC,UAAUC,eAAe,IAAI,EAC7BC,QAAQ,EACR/B,OAAO,EACPgC,OAAO,EACP/B,MAAM,EACNgC,OAAO,EACPC,cAAcC,gBAAgB,EAC9BC,cAAcC,gBAAgB,EAC9BC,iBAAiB,KAAK,EACtBpC,UAAU,EACVqC,KAAKC,YAAY,EACjBC,uBAAuB,EACvB,GAAGC,WACJ,GAAGvB;IAEJK,WAAWI;IAEX,IACEU,kBACC,CAAA,OAAOd,aAAa,YAAY,OAAOA,aAAa,QAAO,GAC5D;QACAA,WAAAA,WAAAA,GAAW,CAAA,GAAA,YAAA,GAAA,EAACmB,KAAAA;sBAAGnB;;IACjB;IAEA,MAAMoB,SAAS9B,OAAAA,OAAK,CAAC+B,UAAU,CAACC,+BAAAA,gBAAgB;IAEhD,MAAMC,kBAAkBjB,iBAAiB;IACzC;;;;;;GAMC,GACD,MAAMkB,kBACJlB,iBAAiB,OAAOmB,oBAAAA,YAAY,CAACC,IAAI,GAAGD,oBAAAA,YAAY,CAACE,IAAI;IAE/D,wCAA2C;QACzC,SAASI,gBAAgBC,IAIxB;YACC,OAAO,OAAA,cAKN,CALM,IAAIC,MACR,iCAA+BD,KAAKE,GAAG,GAAC,iBAAeF,KAAKG,QAAQ,GAAC,4BAA4BH,KAAKI,MAAM,GAAC,eAC3G,CAAA,OAAOC,WAAW,cACf,qEACA,EAAC,IAJF,qBAAA;uBAAA;4BAAA;8BAAA;YAKP;QACF;QAEA,sCAAsC;QACtC,MAAMC,qBAAsD;YAC1DjE,MAAM;QACR;QACA,MAAMkE,gBAAqCC,OAAOC,IAAI,CACpDH;QAEFC,cAAcG,OAAO,CAAC,CAACR;YACrB,IAAIA,QAAQ,QAAQ;gBAClB,IACEvC,KAAK,CAACuC,IAAI,IAAI,QACb,OAAOvC,KAAK,CAACuC,IAAI,KAAK,YAAY,OAAOvC,KAAK,CAACuC,IAAI,KAAK,UACzD;oBACA,MAAMH,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQzC,KAAK,CAACuC,IAAI,KAAK,OAAO,SAAS,OAAOvC,KAAK,CAACuC,IAAI;oBAC1D;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMS,IAAWT;YACnB;QACF;QAEA,sCAAsC;QACtC,MAAMU,qBAAsD;YAC1DtE,IAAI;YACJE,SAAS;YACTC,QAAQ;YACR+B,SAAS;YACTD,UAAU;YACVF,UAAU;YACVY,yBAAyB;YACzBR,SAAS;YACTC,cAAc;YACdE,cAAc;YACdE,gBAAgB;YAChBpC,YAAY;QACd;QACA,MAAMmE,gBAAqCL,OAAOC,IAAI,CACpDG;QAEFC,cAAcH,OAAO,CAAC,CAACR;YACrB,MAAMY,UAAU,OAAOnD,KAAK,CAACuC,IAAI;YAEjC,IAAIA,QAAQ,MAAM;gBAChB,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAYA,YAAY,UAAU;oBAC9D,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,kBACRA,QAAQ,kBACRA,QAAQ,cACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAIY,YAAY,YAAY;oBACxC,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO,IACLZ,QAAQ,aACRA,QAAQ,YACRA,QAAQ,aACRA,QAAQ,cACRA,QAAQ,cACRA,QAAQ,oBACRA,QAAQ,2BACR;gBACA,IAAIvC,KAAK,CAACuC,IAAI,IAAI,QAAQY,YAAY,WAAW;oBAC/C,MAAMf,gBAAgB;wBACpBG;wBACAC,UAAU;wBACVC,QAAQU;oBACV;gBACF;YACF,OAAO;gBACL,sCAAsC;gBACtC,6DAA6D;gBAC7D,MAAMH,IAAWT;YACnB;QACF;IACF;IAEA,IAAIN,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;QACzC,IAAInC,MAAMoD,MAAM,EAAE;YAChBC,CAAAA,GAAAA,UAAAA,QAAQ,EACN;QAEJ;QACA,IAAI,CAAC7C,QAAQ;YACX,IAAI9B;YACJ,IAAI,OAAO6B,aAAa,UAAU;gBAChC7B,OAAO6B;YACT,OAAO,IACL,OAAOA,aAAa,YACpB,OAAOA,SAAS+C,QAAQ,KAAK,UAC7B;gBACA5E,OAAO6B,SAAS+C,QAAQ;YAC1B;YAEA,IAAI5E,MAAM;gBACR,MAAM6E,oBAAoB7E,KACvB8E,KAAK,CAAC,KACNC,IAAI,CAAC,CAACC,UAAYA,QAAQC,UAAU,CAAC,QAAQD,QAAQE,QAAQ,CAAC;gBAEjE,IAAIL,mBAAmB;oBACrB,MAAM,OAAA,cAEL,CAFK,IAAIjB,MACP,mBAAiB5D,OAAK,6IADnB,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;IACF;IAEA,MAAM,EAAEA,IAAI,EAAEC,EAAE,EAAE,GAAGgB,OAAAA,OAAK,CAACkE,OAAO;iCAAC;YACjC,MAAMC,eAAejE,kBAAkBU;YACvC,OAAO;gBACL7B,MAAMoF;gBACNnF,IAAI6B,SAASX,kBAAkBW,UAAUsD;YAC3C;QACF;gCAAG;QAACvD;QAAUC;KAAO;IAErB,oFAAoF;IACpF,IAAIuD;IACJ,IAAI5C,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAIrB,SAAS;gBACXkD,QAAQC,IAAI,CACT,oDAAoD1D,WAAS;YAElE;YACA,IAAIS,kBAAkB;gBACpBgD,QAAQC,IAAI,CACT,yDAAyD1D,WAAS;YAEvE;YACA,IAAI;gBACFwD,QAAQpE,OAAAA,OAAK,CAACuE,QAAQ,CAACC,IAAI,CAAC9D;YAC9B,EAAE,OAAO+D,KAAK;gBACZ,IAAI,CAAC/D,UAAU;oBACb,MAAM,OAAA,cAEL,CAFK,IAAIiC,MACP,uDAAuD/B,WAAS,kFAD7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBACA,MAAM,OAAA,cAKL,CALK,IAAI+B,MACP,6DAA6D/B,WAAS,8FACpE,CAAA,OAAOmC,WAAW,cACf,sEACA,EAAC,IAJH,qBAAA;2BAAA;gCAAA;kCAAA;gBAKN;YACF;QACF,OAAO;;QAEP;IACF,OAAO;QACL,IAAIT,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1C,IAAI,CAAC9B,YAAAA,OAAAA,KAAAA,IAAAA,SAAkBgE,IAAI,MAAK,KAAK;gBACnC,MAAM,OAAA,cAEL,CAFK,IAAI/B,MACR,oKADI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAMgC,WAAgBnD,iBAClB4C,SAAS,OAAOA,UAAU,YAAYA,MAAM3C,GAAG,GAC/CC;IAEJ,4EAA4E;IAC5E,sEAAsE;IACtE,4EAA4E;IAC5E,6BAA6B;IAC7B,MAAMkD,+BAA+B5E,OAAAA,OAAK,CAAC6E,WAAW;mEACpD,CAACC;YACC,IAAIhD,WAAW,MAAM;gBACnB7C,gBAAgBc,OAAO,GAAGgF,CAAAA,GAAAA,OAAAA,iBAAiB,EACzCD,SACA/F,MACA+C,QACAI,iBACAD,iBACA1B;YAEJ;YAEA;2EAAO;oBACL,IAAItB,gBAAgBc,OAAO,EAAE;wBAC3BiF,CAAAA,GAAAA,OAAAA,+BAA+B,EAAC/F,gBAAgBc,OAAO;wBACvDd,gBAAgBc,OAAO,GAAG;oBAC5B;oBACAkF,CAAAA,GAAAA,OAAAA,2BAA2B,EAACH;gBAC9B;;QACF;kEACA;QAAC7C;QAAiBlD;QAAM+C;QAAQI;QAAiB3B;KAAwB;IAG3E,MAAM2E,YAAYC,CAAAA,GAAAA,cAAAA,YAAY,EAACP,8BAA8BD;IAE7D,MAAMS,aAMF;QACF3D,KAAKyD;QACL/D,SAAQrC,CAAC;YACP,IAAIwD,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAc;gBACzC,IAAI,CAAC1D,GAAG;oBACN,MAAM,OAAA,cAEL,CAFK,IAAI6D,MACP,mFADG,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;YAEA,IAAI,CAACnB,kBAAkB,OAAOL,YAAY,YAAY;gBACpDA,QAAQrC;YACV;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACc,OAAO,KAAK,YAC/B;gBACAiD,MAAM/D,KAAK,CAACc,OAAO,CAACrC;YACtB;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAIhD,EAAEuG,gBAAgB,EAAE;gBACtB;YACF;YAEAxG,YAAYC,GAAGC,MAAMC,IAAIC,iBAAiBC,SAASC,QAAQC;QAC7D;QACAgC,cAAatC,CAAC;YACZ,IAAI,CAAC0C,kBAAkB,OAAOH,qBAAqB,YAAY;gBAC7DA,iBAAiBvC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACe,YAAY,KAAK,YACpC;gBACAgD,MAAM/D,KAAK,CAACe,YAAY,CAACtC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,mBAAmBK,QAAQC,GAAG,CAACC,IAA4B,IAApB,KAAK;gBAC/C;YACF;;YAEA,MAAM8C,2BAA2B3D,4BAA4B;QAK/D;QACAL,cAAcgB,QAAQC,GAAG,CAACiD,0BAA0B,GAChDC,oCACA,SAASnE,aAAaxC,CAAC;YACrB,IAAI,CAAC0C,kBAAkB,OAAOD,qBAAqB,YAAY;gBAC7DA,iBAAiBzC;YACnB;YAEA,IACE0C,kBACA4C,MAAM/D,KAAK,IACX,OAAO+D,MAAM/D,KAAK,CAACiB,YAAY,KAAK,YACpC;gBACA8C,MAAM/D,KAAK,CAACiB,YAAY,CAACxC;YAC3B;YAEA,IAAI,CAACgD,QAAQ;gBACX;YACF;YAEA,IAAI,CAACG,iBAAiB;gBACpB;YACF;YAEA,MAAMqD,2BAA2B3D,4BAA4B;YAC7D4D,CAAAA,GAAAA,OAAAA,kBAAkB,EAChBzG,EAAEV,aAAa,EACfkH;QAEJ;IACN;IAEA,6FAA6F;IAC7F,wFAAwF;IACxF,2EAA2E;IAC3E,IAAII,CAAAA,GAAAA,OAAAA,aAAa,EAAC1G,KAAK;QACrBoG,WAAWrG,IAAI,GAAGC;IACpB,OAAO,IACL,CAACwC,kBACDP,YACCmD,MAAMM,IAAI,KAAK,OAAO,CAAE,CAAA,UAAUN,MAAM/D,KAAI,GAC7C;QACA+E,WAAWrG,IAAI,GAAG4G,CAAAA,GAAAA,aAAAA,WAAW,EAAC3G;IAChC;IAEA,IAAI4G;IAEJ,IAAIpE,gBAAgB;QAClB,IAAIc,QAAQC,GAAG,CAACC,QAAQ,KAAK,WAAe;YAC1CqD,CAAAA,GAAAA,WAAAA,SAAS,EACP,oEACE,oEACA,4CACA;QAEN;QACAD,OAAAA,WAAAA,GAAO5F,OAAAA,OAAK,CAAC8F,YAAY,CAAC1B,OAAOgB;IACnC,OAAO;QACLQ,OAAAA,WAAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAAC/D,KAAAA;YAAG,GAAGD,SAAS;YAAG,GAAGwD,UAAU;sBAC7B1E;;IAGP;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,YAAA,GAAA,EAACqF,kBAAkBC,QAAQ,EAAA;QAACC,OAAO3F;kBAChCsF;;AAGP;AAEA,MAAMG,oBAAAA,WAAAA,GAAoBG,CAAAA,GAAAA,OAAAA,aAAa,EAErCzF,OAAAA,gBAAgB;AAEX,MAAMzC,gBAAgB;IAC3B,OAAO+D,CAAAA,GAAAA,OAAAA,UAAU,EAACgE;AACpB", "ignoreList": [0], "debugId": null}}]}