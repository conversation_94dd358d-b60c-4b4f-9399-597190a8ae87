'use client'

import { useEffect, useState } from 'react'
import { getSupabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface FinanceData {
  id?: string
  date: string
  dashsy_revenue: number
  syriana_revenue: number
  booking_revenue: number
  templates_revenue: number
  apps_revenue: number
  other_revenue: number
  total_expenses: number
  net_profit: number
  finance_notes: string
}

export default function FinancePage() {
  const [user, setUser] = useState<any>(null)
  const [financeData, setFinanceData] = useState<FinanceData>({
    date: new Date().toISOString().split('T')[0],
    dashsy_revenue: 0,
    syriana_revenue: 0,
    booking_revenue: 0,
    templates_revenue: 0,
    apps_revenue: 0,
    other_revenue: 0,
    total_expenses: 0,
    net_profit: 0,
    finance_notes: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadFinanceData()
    }
  }, [user, financeData.date])

  const checkUser = async () => {
    const supabase = await getSupabase()
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
    } else {
      setUser(user)
    }
    setLoading(false)
  }

  const loadFinanceData = async () => {
    try {
      const { data, error } = await supabase
        .from('finance_records')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', financeData.date)
        .single()

      if (data) {
        setFinanceData({
          id: data.id,
          date: data.date,
          dashsy_revenue: data.dashsy_revenue || 0,
          syriana_revenue: data.syriana_revenue || 0,
          booking_revenue: data.booking_revenue || 0,
          templates_revenue: data.templates_revenue || 0,
          apps_revenue: data.apps_revenue || 0,
          other_revenue: data.other_revenue || 0,
          total_expenses: data.total_expenses || 0,
          net_profit: data.net_profit || 0,
          finance_notes: data.finance_notes || ''
        })
      }
    } catch (error) {
      console.error('Error loading finance data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateTotalRevenue = () => {
    return (
      financeData.dashsy_revenue +
      financeData.syriana_revenue +
      financeData.booking_revenue +
      financeData.templates_revenue +
      financeData.apps_revenue +
      financeData.other_revenue
    )
  }

  const calculateNetProfit = () => {
    return calculateTotalRevenue() - financeData.total_expenses
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const totalRevenue = calculateTotalRevenue()
      const netProfit = calculateNetProfit()
      
      const dataToSave = {
        user_id: user.id,
        date: financeData.date,
        dashsy_revenue: financeData.dashsy_revenue,
        syriana_revenue: financeData.syriana_revenue,
        booking_revenue: financeData.booking_revenue,
        templates_revenue: financeData.templates_revenue,
        apps_revenue: financeData.apps_revenue,
        other_revenue: financeData.other_revenue,
        total_revenue: totalRevenue,
        total_expenses: financeData.total_expenses,
        net_profit: netProfit,
        finance_notes: financeData.finance_notes
      }

      if (financeData.id) {
        await supabase
          .from('finance_records')
          .update(dataToSave)
          .eq('id', financeData.id)
      } else {
        const { data } = await supabase
          .from('finance_records')
          .insert(dataToSave)
          .select()
          .single()
        
        if (data) {
          setFinanceData(prev => ({ ...prev, id: data.id }))
        }
      }

      alert('تم حفظ البيانات بنجاح!')
    } catch (error) {
      console.error('Error saving finance data:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handleRevenueChange = (field: keyof FinanceData, value: number) => {
    setFinanceData(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  const totalRevenue = calculateTotalRevenue()
  const netProfit = calculateNetProfit()

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للوحة الرئيسية
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                💰 الإيرادات والمالية
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                إجمالي: ${totalRevenue.toFixed(2)}
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                netProfit >= 0 ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800'
              }`}>
                صافي: ${netProfit.toFixed(2)}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Date Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التاريخ
              </label>
              <input
                type="date"
                value={financeData.date}
                onChange={(e) => setFinanceData(prev => ({ ...prev, date: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Revenue Sources */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">مصادر الإيرادات</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      🚀 Dash-sy ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={financeData.dashsy_revenue}
                      onChange={(e) => handleRevenueChange('dashsy_revenue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      🛒 Syriana Store ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={financeData.syriana_revenue}
                      onChange={(e) => handleRevenueChange('syriana_revenue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      📅 Booking-Sy ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={financeData.booking_revenue}
                      onChange={(e) => handleRevenueChange('booking_revenue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      🎨 القوالب ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={financeData.templates_revenue}
                      onChange={(e) => handleRevenueChange('templates_revenue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      📱 التطبيقات ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={financeData.apps_revenue}
                      onChange={(e) => handleRevenueChange('apps_revenue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      💼 إيرادات أخرى ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={financeData.other_revenue}
                      onChange={(e) => handleRevenueChange('other_revenue', parseFloat(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Expenses */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">المصروفات</h3>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    💸 إجمالي المصروفات ($)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={financeData.total_expenses}
                    onChange={(e) => handleRevenueChange('total_expenses', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Summary */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">الملخص المالي</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="text-sm text-green-600 font-medium">إجمالي الإيرادات</p>
                    <p className="text-2xl font-bold text-green-800">${totalRevenue.toFixed(2)}</p>
                  </div>
                  <div className="bg-red-50 p-4 rounded-lg">
                    <p className="text-sm text-red-600 font-medium">إجمالي المصروفات</p>
                    <p className="text-2xl font-bold text-red-800">${financeData.total_expenses.toFixed(2)}</p>
                  </div>
                  <div className={`p-4 rounded-lg ${netProfit >= 0 ? 'bg-blue-50' : 'bg-red-50'}`}>
                    <p className={`text-sm font-medium ${netProfit >= 0 ? 'text-blue-600' : 'text-red-600'}`}>
                      صافي الربح
                    </p>
                    <p className={`text-2xl font-bold ${netProfit >= 0 ? 'text-blue-800' : 'text-red-800'}`}>
                      ${netProfit.toFixed(2)}
                    </p>
                  </div>
                </div>
              </div>

              {/* Finance Notes */}
              <div className="border-t pt-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات مالية
                </label>
                <textarea
                  value={financeData.finance_notes}
                  onChange={(e) => setFinanceData(prev => ({ ...prev, finance_notes: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب ملاحظاتك المالية هنا..."
                />
              </div>
            </div>

            <div className="mt-8 pt-6 border-t">
              <button
                onClick={handleSave}
                disabled={saving}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ البيانات'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
