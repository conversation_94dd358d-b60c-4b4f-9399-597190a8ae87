'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts'

interface AnalyticsData {
  worshipTrend: any[]
  learningTrend: any[]
  revenueTrend: any[]
  projectProgress: any[]
  monthlyStats: any
}

export default function AnalyticsPage() {
  const [user, setUser] = useState<any>(null)
  const [data, setData] = useState<AnalyticsData>({
    worshipTrend: [],
    learningTrend: [],
    revenueTrend: [],
    projectProgress: [],
    monthlyStats: {}
  })
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30') // days
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadAnalyticsData()
    }
  }, [user, timeRange])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
    } else {
      setUser(user)
    }
  }

  const loadAnalyticsData = async () => {
    try {
      const endDate = new Date()
      const startDate = new Date()
      startDate.setDate(endDate.getDate() - parseInt(timeRange))

      const startDateStr = startDate.toISOString().split('T')[0]
      const endDateStr = endDate.toISOString().split('T')[0]

      // Load worship trend
      const { data: worshipData } = await supabase
        .from('worship_records')
        .select('date, worship_percentage')
        .eq('user_id', user.id)
        .gte('date', startDateStr)
        .lte('date', endDateStr)
        .order('date')

      // Load learning trend
      const { data: learningData } = await supabase
        .from('learning_records')
        .select('date, total_hours, laravel_time, nodejs_time, nextjs_time, flutter_time, database_time')
        .eq('user_id', user.id)
        .gte('date', startDateStr)
        .lte('date', endDateStr)
        .order('date')

      // Load revenue trend
      const { data: revenueData } = await supabase
        .from('finance_records')
        .select('date, total_revenue, net_profit')
        .eq('user_id', user.id)
        .gte('date', startDateStr)
        .lte('date', endDateStr)
        .order('date')

      // Load project progress
      const { data: projectData } = await supabase
        .from('project_records')
        .select('date, dashsy_progress, syriana_progress, booking_progress')
        .eq('user_id', user.id)
        .gte('date', startDateStr)
        .lte('date', endDateStr)
        .order('date')

      setData({
        worshipTrend: worshipData || [],
        learningTrend: learningData?.map(item => ({
          ...item,
          Laravel: item.laravel_time,
          'Node.js': item.nodejs_time,
          'Next.js': item.nextjs_time,
          Flutter: item.flutter_time,
          Database: item.database_time
        })) || [],
        revenueTrend: revenueData || [],
        projectProgress: projectData?.map(item => ({
          ...item,
          'Dash-sy': item.dashsy_progress,
          'Syriana Store': item.syriana_progress,
          'Booking-Sy': item.booking_progress
        })) || [],
        monthlyStats: {
          totalWorshipDays: worshipData?.length || 0,
          avgWorshipPercentage: worshipData?.reduce((sum, item) => sum + item.worship_percentage, 0) / (worshipData?.length || 1) || 0,
          totalLearningHours: learningData?.reduce((sum, item) => sum + item.total_hours, 0) || 0,
          totalRevenue: revenueData?.reduce((sum, item) => sum + item.total_revenue, 0) || 0,
          totalProfit: revenueData?.reduce((sum, item) => sum + item.net_profit, 0) || 0
        }
      })
    } catch (error) {
      console.error('Error loading analytics data:', error)
    } finally {
      setLoading(false)
    }
  }

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4']

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري تحميل التحليلات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للوحة الرئيسية
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                📈 التحليلات والإحصائيات
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-1 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="7">آخر 7 أيام</option>
                <option value="30">آخر 30 يوم</option>
                <option value="90">آخر 3 أشهر</option>
                <option value="365">آخر سنة</option>
              </select>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">🕌</span>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">متوسط العبادات</p>
                <p className="text-2xl font-bold text-blue-600">
                  {Math.round(data.monthlyStats.avgWorshipPercentage)}%
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📚</span>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي ساعات التعلم</p>
                <p className="text-2xl font-bold text-green-600">
                  {Math.round(data.monthlyStats.totalLearningHours)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">💰</span>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">إجمالي الإيرادات</p>
                <p className="text-2xl font-bold text-yellow-600">
                  ${Math.round(data.monthlyStats.totalRevenue)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <span className="text-2xl">📊</span>
              </div>
              <div className="mr-4">
                <p className="text-sm font-medium text-gray-600">صافي الربح</p>
                <p className="text-2xl font-bold text-purple-600">
                  ${Math.round(data.monthlyStats.totalProfit)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Worship Trend */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">اتجاه العبادات</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={data.worshipTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="worship_percentage" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  name="نسبة العبادات %"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Learning Hours */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">ساعات التعلم اليومية</h3>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={data.learningTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Area 
                  type="monotone" 
                  dataKey="Laravel" 
                  stackId="1" 
                  stroke="#3B82F6" 
                  fill="#3B82F6"
                />
                <Area 
                  type="monotone" 
                  dataKey="Node.js" 
                  stackId="1" 
                  stroke="#10B981" 
                  fill="#10B981"
                />
                <Area 
                  type="monotone" 
                  dataKey="Next.js" 
                  stackId="1" 
                  stroke="#F59E0B" 
                  fill="#F59E0B"
                />
                <Area 
                  type="monotone" 
                  dataKey="Flutter" 
                  stackId="1" 
                  stroke="#EF4444" 
                  fill="#EF4444"
                />
                <Area 
                  type="monotone" 
                  dataKey="Database" 
                  stackId="1" 
                  stroke="#8B5CF6" 
                  fill="#8B5CF6"
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>

          {/* Revenue Trend */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">اتجاه الإيرادات</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={data.revenueTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="total_revenue" fill="#10B981" name="إجمالي الإيرادات" />
                <Bar dataKey="net_profit" fill="#3B82F6" name="صافي الربح" />
              </BarChart>
            </ResponsiveContainer>
          </div>

          {/* Project Progress */}
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">تقدم المشاريع</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={data.projectProgress}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="Dash-sy" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                />
                <Line 
                  type="monotone" 
                  dataKey="Syriana Store" 
                  stroke="#10B981" 
                  strokeWidth={2}
                />
                <Line 
                  type="monotone" 
                  dataKey="Booking-Sy" 
                  stroke="#F59E0B" 
                  strokeWidth={2}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>
      </main>
    </div>
  )
}
