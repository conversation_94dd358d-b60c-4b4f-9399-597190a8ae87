{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse, type NextRequest } from 'next/server'\n\nexport async function middleware(request: NextRequest) {\n  // For now, allow all requests to pass through\n  // Authentication will be handled client-side\n  \n  const publicPaths = ['/login', '/register', '/api']\n  const isPublicPath = publicPaths.some(path => \n    request.nextUrl.pathname.startsWith(path)\n  )\n  \n  // Allow public paths and API routes\n  if (isPublicPath) {\n    return NextResponse.next()\n  }\n  \n  // For protected routes, let the client-side handle authentication\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    /*\n     * Match all request paths except for the ones starting with:\n     * - _next/static (static files)\n     * - _next/image (image optimization files)\n     * - favicon.ico (favicon file)\n     */\n    '/((?!_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEO,eAAe,WAAW,OAAoB;IACnD,8CAA8C;IAC9C,6CAA6C;IAE7C,MAAM,cAAc;QAAC;QAAU;QAAa;KAAO;IACnD,MAAM,eAAe,YAAY,IAAI,CAAC,CAAA,OACpC,QAAQ,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC;IAGtC,oCAAoC;IACpC,IAAI,cAAc;QAChB,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEA,kEAAkE;IAClE,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;KAKC,GACD;KACD;AACH"}}]}