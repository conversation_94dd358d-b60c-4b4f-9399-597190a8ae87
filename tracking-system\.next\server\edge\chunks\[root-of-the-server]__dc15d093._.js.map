{"version": 3, "sources": [], "sections": [{"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["// Temporarily disabled middleware\n// import { updateSession } from '@/lib/auth'\n\n// export async function middleware(request: any) {\n//   return await updateSession(request)\n// }\n\n// export const config = {\n//   matcher: [\n//     /*\n//      * Match all request paths except for the ones starting with:\n//      * - _next/static (static files)\n//      * - _next/image (image optimization files)\n//      * - favicon.ico (favicon file)\n//      * Feel free to modify this pattern to include more paths.\n//      */\n//     '/((?!_next/static|_next/image|favicon.ico|.*\\\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',\n//   ],\n// }\n"], "names": [], "mappings": "AAAA,kCAAkC;AAClC,6CAA6C;AAE7C,mDAAmD;AACnD,wCAAwC;AACxC,IAAI;AAEJ,0BAA0B;AAC1B,eAAe;AACf,SAAS;AACT,oEAAoE;AACpE,uCAAuC;AACvC,kDAAkD;AAClD,sCAAsC;AACtC,iEAAiE;AACjE,UAAU;AACV,2FAA2F;AAC3F,OAAO;AACP,IAAI"}}]}