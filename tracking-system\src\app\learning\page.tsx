'use client'

import { useEffect, useState } from 'react'
import { supabase } from '@/lib/supabase'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

interface LearningData {
  id?: string
  date: string
  laravel_time: number
  nodejs_time: number
  nextjs_time: number
  flutter_time: number
  database_time: number
  laravel_progress: number
  nodejs_progress: number
  nextjs_progress: number
  flutter_progress: number
  database_progress: number
  learning_notes: string
}

export default function LearningPage() {
  const [user, setUser] = useState<any>(null)
  const [learningData, setLearningData] = useState<LearningData>({
    date: new Date().toISOString().split('T')[0],
    laravel_time: 0,
    nodejs_time: 0,
    nextjs_time: 0,
    flutter_time: 0,
    database_time: 0,
    laravel_progress: 0,
    nodejs_progress: 0,
    nextjs_progress: 0,
    flutter_progress: 0,
    database_progress: 0,
    learning_notes: ''
  })
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const router = useRouter()

  useEffect(() => {
    checkUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadLearningData()
    }
  }, [user, learningData.date])

  const checkUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    if (!user) {
      router.push('/login')
    } else {
      setUser(user)
    }
  }

  const loadLearningData = async () => {
    try {
      const { data, error } = await supabase
        .from('learning_records')
        .select('*')
        .eq('user_id', user.id)
        .eq('date', learningData.date)
        .single()

      if (data) {
        setLearningData({
          id: data.id,
          date: data.date,
          laravel_time: data.laravel_time || 0,
          nodejs_time: data.nodejs_time || 0,
          nextjs_time: data.nextjs_time || 0,
          flutter_time: data.flutter_time || 0,
          database_time: data.database_time || 0,
          laravel_progress: data.laravel_progress || 0,
          nodejs_progress: data.nodejs_progress || 0,
          nextjs_progress: data.nextjs_progress || 0,
          flutter_progress: data.flutter_progress || 0,
          database_progress: data.database_progress || 0,
          learning_notes: data.learning_notes || ''
        })
      }
    } catch (error) {
      console.error('Error loading learning data:', error)
    } finally {
      setLoading(false)
    }
  }

  const calculateTotalHours = () => {
    return (
      learningData.laravel_time +
      learningData.nodejs_time +
      learningData.nextjs_time +
      learningData.flutter_time +
      learningData.database_time
    ) / 60 // Convert minutes to hours
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const totalHours = calculateTotalHours()
      const dataToSave = {
        user_id: user.id,
        date: learningData.date,
        laravel_time: learningData.laravel_time,
        nodejs_time: learningData.nodejs_time,
        nextjs_time: learningData.nextjs_time,
        flutter_time: learningData.flutter_time,
        database_time: learningData.database_time,
        laravel_progress: learningData.laravel_progress,
        nodejs_progress: learningData.nodejs_progress,
        nextjs_progress: learningData.nextjs_progress,
        flutter_progress: learningData.flutter_progress,
        database_progress: learningData.database_progress,
        total_hours: totalHours,
        learning_notes: learningData.learning_notes
      }

      if (learningData.id) {
        await supabase
          .from('learning_records')
          .update(dataToSave)
          .eq('id', learningData.id)
      } else {
        const { data } = await supabase
          .from('learning_records')
          .insert(dataToSave)
          .select()
          .single()
        
        if (data) {
          setLearningData(prev => ({ ...prev, id: data.id }))
        }
      }

      alert('تم حفظ البيانات بنجاح!')
    } catch (error) {
      console.error('Error saving learning data:', error)
      alert('حدث خطأ أثناء حفظ البيانات')
    } finally {
      setSaving(false)
    }
  }

  const handleTimeChange = (field: keyof LearningData, value: number) => {
    setLearningData(prev => ({ ...prev, [field]: value }))
  }

  const handleProgressChange = (field: keyof LearningData, value: number) => {
    setLearningData(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">جاري التحميل...</p>
        </div>
      </div>
    )
  }

  const totalHours = calculateTotalHours()

  return (
    <div className="min-h-screen bg-gray-50" dir="rtl">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href="/dashboard" className="text-blue-600 hover:text-blue-800 ml-4">
                ← العودة للوحة الرئيسية
              </Link>
              <h1 className="text-xl font-semibold text-gray-900">
                📚 متابعة التعلم والتطوير
              </h1>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                إجمالي: {totalHours.toFixed(1)} ساعة
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-lg shadow">
          <div className="p-6">
            {/* Date Selector */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                التاريخ
              </label>
              <input
                type="date"
                value={learningData.date}
                onChange={(e) => setLearningData(prev => ({ ...prev, date: e.target.value }))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Learning Sections */}
            <div className="space-y-8">
              {/* Laravel */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🔴</span>
                  Laravel
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={learningData.laravel_time}
                      onChange={(e) => handleTimeChange('laravel_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={learningData.laravel_progress}
                      onChange={(e) => handleProgressChange('laravel_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Node.js */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🟢</span>
                  Node.js
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={learningData.nodejs_time}
                      onChange={(e) => handleTimeChange('nodejs_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={learningData.nodejs_progress}
                      onChange={(e) => handleProgressChange('nodejs_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Next.js */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">⚫</span>
                  Next.js
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={learningData.nextjs_time}
                      onChange={(e) => handleTimeChange('nextjs_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={learningData.nextjs_progress}
                      onChange={(e) => handleProgressChange('nextjs_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Flutter */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🔵</span>
                  Flutter
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={learningData.flutter_time}
                      onChange={(e) => handleTimeChange('flutter_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={learningData.flutter_progress}
                      onChange={(e) => handleProgressChange('flutter_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Database */}
              <div className="border-b pb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="text-2xl ml-2">🗄️</span>
                  قواعد البيانات
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      الوقت المستغرق (دقيقة)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={learningData.database_time}
                      onChange={(e) => handleTimeChange('database_time', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      نسبة التقدم (%)
                    </label>
                    <input
                      type="number"
                      min="0"
                      max="100"
                      value={learningData.database_progress}
                      onChange={(e) => handleProgressChange('database_progress', parseInt(e.target.value) || 0)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Learning Notes */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ملاحظات التعلم
                </label>
                <textarea
                  value={learningData.learning_notes}
                  onChange={(e) => setLearningData(prev => ({ ...prev, learning_notes: e.target.value }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="اكتب ملاحظاتك حول التعلم اليوم..."
                />
              </div>
            </div>

            <div className="mt-8 pt-6 border-t">
              <button
                onClick={handleSave}
                disabled={saving}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                {saving ? 'جاري الحفظ...' : 'حفظ البيانات'}
              </button>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
