{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/signup/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { getSupabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\nexport default function SignupPage() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState(false)\n  const router = useRouter()\n\n  const handleSignup = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          }\n        }\n      })\n\n      if (error) {\n        setError(error.message)\n      } else {\n        setSuccess(true)\n        setTimeout(() => {\n          router.push('/login')\n        }, 3000)\n      }\n    } catch (err) {\n      setError('حدث خطأ غير متوقع')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (success) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100\" dir=\"rtl\">\n        <div className=\"max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg text-center\">\n          <div className=\"text-green-600\">\n            <svg className=\"mx-auto h-16 w-16\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">تم إنشاء الحساب بنجاح!</h2>\n          <p className=\"text-gray-600\">\n            تم إرسال رابط التفعيل إلى بريدك الإلكتروني. يرجى التحقق من البريد وتفعيل الحساب.\n          </p>\n          <p className=\"text-sm text-gray-500\">\n            سيتم توجيهك إلى صفحة تسجيل الدخول خلال 3 ثوانٍ...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\" dir=\"rtl\">\n      <div className=\"max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg\">\n        <div className=\"text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            🕌 نظام المتابعة الشامل\n          </h2>\n          <p className=\"text-gray-600\">إنشاء حساب جديد</p>\n        </div>\n\n        <form className=\"space-y-6\" onSubmit={handleSignup}>\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {error}\n            </div>\n          )}\n\n          <div>\n            <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              الاسم الكامل\n            </label>\n            <input\n              id=\"fullName\"\n              type=\"text\"\n              required\n              value={fullName}\n              onChange={(e) => setFullName(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"أدخل اسمك الكامل\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              البريد الإلكتروني\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              required\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"أدخل بريدك الإلكتروني\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              كلمة المرور\n            </label>\n            <input\n              id=\"password\"\n              type=\"password\"\n              required\n              minLength={6}\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"أدخل كلمة المرور (6 أحرف على الأقل)\"\n            />\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? 'جاري إنشاء الحساب...' : 'إنشاء حساب'}\n          </button>\n\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              لديك حساب بالفعل؟{' '}\n              <Link href=\"/login\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n                تسجيل الدخول\n              </Link>\n            </p>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;oBACb;gBACF;YACF;YAEA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB,OAAO;gBACL,WAAW;gBACX,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;YAA+F,KAAI;sBAChH,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAoB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC3E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAG7B,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA6F,KAAI;kBAC9G,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,8OAAC;oBAAK,WAAU;oBAAY,UAAU;;wBACnC,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,WAAW;oCACX,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,UAAU,yBAAyB;;;;;;sCAGtC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCACjB;kDAClB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5F", "debugId": null}}]}