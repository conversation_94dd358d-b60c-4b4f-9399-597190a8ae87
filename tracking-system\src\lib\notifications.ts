'use client'

export interface NotificationData {
  title: string
  body: string
  icon?: string
  badge?: string
  tag?: string
  data?: any
}

class NotificationService {
  private permission: NotificationPermission = 'default'

  constructor() {
    if (typeof window !== 'undefined' && 'Notification' in window) {
      this.permission = Notification.permission
    }
  }

  async requestPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications')
      return false
    }

    if (this.permission === 'granted') {
      return true
    }

    if (this.permission === 'denied') {
      return false
    }

    const permission = await Notification.requestPermission()
    this.permission = permission
    return permission === 'granted'
  }

  async showNotification(data: NotificationData): Promise<boolean> {
    const hasPermission = await this.requestPermission()
    
    if (!hasPermission) {
      return false
    }

    try {
      const notification = new Notification(data.title, {
        body: data.body,
        icon: data.icon || '/icon-192x192.png',
        badge: data.badge || '/icon-192x192.png',
        tag: data.tag,
        data: data.data,
        requireInteraction: true,
        silent: false
      })

      notification.onclick = () => {
        window.focus()
        notification.close()
        
        // Handle notification click based on data
        if (data.data?.url) {
          window.location.href = data.data.url
        }
      }

      // Auto close after 10 seconds
      setTimeout(() => {
        notification.close()
      }, 10000)

      return true
    } catch (error) {
      console.error('Error showing notification:', error)
      return false
    }
  }

  // Prayer time notifications
  showPrayerNotification(prayerName: string, timeLeft: number) {
    const minutes = Math.floor(timeLeft / 60000)
    
    if (minutes <= 5 && minutes > 0) {
      this.showNotification({
        title: `🕌 حان وقت ${prayerName}`,
        body: `باقي ${minutes} دقائق على موعد ${prayerName}`,
        tag: `prayer-${prayerName}`,
        data: { url: '/worship' }
      })
    } else if (minutes <= 0) {
      this.showNotification({
        title: `🕌 حان وقت ${prayerName} الآن`,
        body: `حان الآن موعد صلاة ${prayerName}`,
        tag: `prayer-${prayerName}-now`,
        data: { url: '/worship' }
      })
    }
  }

  // Learning session notifications
  showLearningNotification(sessionType: string) {
    this.showNotification({
      title: '📚 وقت التعلم',
      body: `حان وقت جلسة ${sessionType}. ابدأ الآن!`,
      tag: 'learning-session',
      data: { url: '/learning' }
    })
  }

  // Work session notifications
  showWorkNotification(projectName: string) {
    this.showNotification({
      title: '💼 وقت العمل',
      body: `حان وقت العمل على ${projectName}`,
      tag: 'work-session',
      data: { url: '/projects' }
    })
  }

  // Daily summary notification
  showDailySummaryNotification(completionRate: number) {
    const emoji = completionRate >= 90 ? '🎉' : completionRate >= 70 ? '👍' : '💪'
    const message = completionRate >= 90 ? 'يوم ممتاز!' : 
                   completionRate >= 70 ? 'يوم جيد!' : 'يمكنك تحسين الأداء غداً'
    
    this.showNotification({
      title: `${emoji} ملخص اليوم`,
      body: `معدل الإنجاز: ${completionRate}% - ${message}`,
      tag: 'daily-summary',
      data: { url: '/dashboard' }
    })
  }

  // Goal achievement notification
  showGoalAchievementNotification(goalName: string) {
    this.showNotification({
      title: '🎯 تم تحقيق الهدف!',
      body: `تهانينا! لقد حققت هدف: ${goalName}`,
      tag: 'goal-achievement'
    })
  }

  // Reminder notifications
  showReminderNotification(reminderText: string, type: 'worship' | 'learning' | 'work' | 'break') {
    const icons = {
      worship: '🕌',
      learning: '📚',
      work: '💼',
      break: '☕'
    }

    const urls = {
      worship: '/worship',
      learning: '/learning',
      work: '/projects',
      break: '/dashboard'
    }

    this.showNotification({
      title: `${icons[type]} تذكير`,
      body: reminderText,
      tag: `reminder-${type}`,
      data: { url: urls[type] }
    })
  }
}

// Schedule-based notifications
export class ScheduleNotifications {
  private notificationService: NotificationService
  private intervals: NodeJS.Timeout[] = []

  constructor() {
    this.notificationService = new NotificationService()
  }

  startScheduledNotifications() {
    this.clearAllIntervals()

    // Prayer times (approximate - should be adjusted based on location)
    const prayerTimes = [
      { name: 'الفجر', hour: 5, minute: 30 },
      { name: 'الضحى', hour: 8, minute: 0 },
      { name: 'الظهر', hour: 12, minute: 30 },
      { name: 'العصر', hour: 16, minute: 30 },
      { name: 'المغرب', hour: 18, minute: 50 },
      { name: 'العشاء', hour: 20, minute: 30 }
    ]

    // Learning sessions
    const learningTimes = [
      { name: 'جلسة التعلم الصباحية', hour: 8, minute: 0 }
    ]

    // Work sessions
    const workTimes = [
      { name: 'جلسة العمل الأولى', hour: 13, minute: 30 },
      { name: 'جلسة العمل الثانية', hour: 18, minute: 0 }
    ]

    // Set up prayer notifications
    prayerTimes.forEach(prayer => {
      this.scheduleNotification(prayer.hour, prayer.minute, () => {
        this.notificationService.showPrayerNotification(prayer.name, 0)
      })

      // 5 minutes before
      this.scheduleNotification(prayer.hour, prayer.minute - 5, () => {
        this.notificationService.showPrayerNotification(prayer.name, 5 * 60 * 1000)
      })
    })

    // Set up learning notifications
    learningTimes.forEach(session => {
      this.scheduleNotification(session.hour, session.minute, () => {
        this.notificationService.showLearningNotification(session.name)
      })
    })

    // Set up work notifications
    workTimes.forEach(session => {
      this.scheduleNotification(session.hour, session.minute, () => {
        this.notificationService.showWorkNotification(session.name)
      })
    })

    // Daily summary at 9 PM
    this.scheduleNotification(21, 0, () => {
      // This would need to fetch actual completion rate
      this.notificationService.showDailySummaryNotification(75)
    })
  }

  private scheduleNotification(hour: number, minute: number, callback: () => void) {
    const now = new Date()
    const scheduledTime = new Date()
    scheduledTime.setHours(hour, minute, 0, 0)

    // If the time has passed today, schedule for tomorrow
    if (scheduledTime <= now) {
      scheduledTime.setDate(scheduledTime.getDate() + 1)
    }

    const timeUntilNotification = scheduledTime.getTime() - now.getTime()

    const timeout = setTimeout(() => {
      callback()
      // Reschedule for next day
      this.scheduleNotification(hour, minute, callback)
    }, timeUntilNotification)

    this.intervals.push(timeout)
  }

  clearAllIntervals() {
    this.intervals.forEach(interval => clearTimeout(interval))
    this.intervals = []
  }

  stop() {
    this.clearAllIntervals()
  }
}

export const notificationService = new NotificationService()
export const scheduleNotifications = new ScheduleNotifications()
