// Temporary authentication system using localStorage
interface User {
  id: string
  email: string
  full_name?: string
  created_at: string
}

interface AuthResponse {
  data: { user: User | null }
  error: any
}

// Mock database using localStorage
const getStoredUsers = (): User[] => {
  if (typeof window === 'undefined') return []
  const users = localStorage.getItem('tracking_users')
  return users ? JSON.parse(users) : []
}

const saveUsers = (users: User[]) => {
  if (typeof window === 'undefined') return
  localStorage.setItem('tracking_users', JSON.stringify(users))
}

const getCurrentUser = (): User | null => {
  if (typeof window === 'undefined') return null
  const user = localStorage.getItem('tracking_current_user')
  return user ? JSON.parse(user) : null
}

const setCurrentUser = (user: User | null) => {
  if (typeof window === 'undefined') return
  if (user) {
    localStorage.setItem('tracking_current_user', JSON.stringify(user))
  } else {
    localStorage.removeItem('tracking_current_user')
  }
}

// Initialize demo users if none exist
const initializeDemoUsers = () => {
  if (typeof window === 'undefined') return
  const existingUsers = getStoredUsers()
  if (existingUsers.length === 0) {
    const demoUsers = [
      {
        id: 'demo-user-1',
        email: '<EMAIL>',
        full_name: 'مستخدم تجريبي',
        created_at: new Date().toISOString()
      },
      {
        id: 'user-test-1',
        email: '<EMAIL>',
        full_name: 'سمير الكحلان',
        created_at: new Date().toISOString()
      }
    ]
    saveUsers(demoUsers)
  }
}

// Mock Supabase client
const mockSupabase = {
  auth: {
    getUser: async (): Promise<AuthResponse> => {
      const user = getCurrentUser()
      return { data: { user }, error: null }
    },

    signOut: async () => {
      setCurrentUser(null)
      return { error: null }
    },

    signInWithPassword: async ({ email, password }: { email: string, password: string }) => {
      const users = getStoredUsers()
      const user = users.find(u => u.email === email)

      if (!user) {
        return {
          data: { user: null },
          error: { message: 'البريد الإلكتروني غير مسجل' }
        }
      }

      // For demo, accept any password
      setCurrentUser(user)
      return { data: { user }, error: null }
    },

    signUp: async ({ email, password, options }: {
      email: string,
      password: string,
      options?: { data?: { full_name?: string } }
    }) => {
      const users = getStoredUsers()
      const existingUser = users.find(u => u.email === email)

      if (existingUser) {
        return {
          data: { user: null },
          error: { message: 'البريد الإلكتروني مسجل بالفعل' }
        }
      }

      const newUser: User = {
        id: Date.now().toString(),
        email,
        full_name: options?.data?.full_name,
        created_at: new Date().toISOString()
      }

      users.push(newUser)
      saveUsers(users)
      setCurrentUser(newUser)

      return { data: { user: newUser }, error: null }
    }
  },

  from: (table: string) => ({
    select: (columns?: string) => ({
      eq: (column: string, value: any) => ({
        single: () => Promise.resolve({ data: null, error: null })
      }),
      order: (column: string) => Promise.resolve({ data: [], error: null })
    }),
    insert: (data: any) => Promise.resolve({ data: null, error: null }),
    update: (data: any) => ({
      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })
    }),
    delete: () => ({
      eq: (column: string, value: any) => Promise.resolve({ data: null, error: null })
    }),
    upsert: (data: any) => ({
      select: () => Promise.resolve({ data: null, error: null })
    })
  })
}

// For client-side operations
export const getSupabase = async () => {
  initializeDemoUsers()
  return mockSupabase
}

// For server-side operations
export const createServerClient = async () => {
  return mockSupabase
}

// Database types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface WorshipRecord {
  id: string
  user_id: string
  date: string
  sleep_on_time: boolean
  wake_up_on_time: boolean
  night_prayer: boolean
  fajr_prayer: boolean
  quran_reading: boolean
  istighfar_200: boolean
  salawat_200: boolean
  tasbih_100: boolean
  la_ilaha_illa_allah_100: boolean
  la_ilaha_illa_anta_50: boolean
  morning_exercise: boolean
  shower: boolean
  duha_prayer: boolean
  dhuhr_prayer: boolean
  asr_prayer: boolean
  maghrib_prayer: boolean
  isha_prayer: boolean
  slept_after_fajr: boolean
  worship_percentage: number
  spiritual_notes?: string
  created_at: string
  updated_at: string
}

export interface LearningRecord {
  id: string
  user_id: string
  date: string
  laravel_time: number
  laravel_topic?: string
  laravel_progress: number
  laravel_rating: number
  nodejs_time: number
  nodejs_topic?: string
  nodejs_progress: number
  nextjs_time: number
  nextjs_topic?: string
  nextjs_progress: number
  mean_rating: number
  flutter_time: number
  flutter_topic?: string
  flutter_progress: number
  database_time: number
  database_type?: string
  database_progress: number
  total_hours: number
  key_learnings?: string
  challenges?: string
  tomorrow_plan?: string
  created_at: string
  updated_at: string
}

export interface TemplateRecord {
  id: string
  user_id: string
  date: string
  shopify_time: number
  shopify_features?: string
  shopify_progress: number
  shopify_issues?: string
  shopify_solutions?: string
  salla_progress: number
  salla_notes?: string
  wordpress_progress: number
  wordpress_notes?: string
  platform_api?: string
  platform_functions?: string
  platform_testing: boolean
  wholesale_prices?: string
  expected_revenue: number
  tomorrow_plan?: string
  created_at: string
  updated_at: string
}

export interface AppRecord {
  id: string
  user_id: string
  date: string
  shopify_functions?: string
  shopify_integration: string
  shopify_api_test: string
  shopify_status: string
  salla_functions?: string
  salla_integration: string
  salla_api_test: string
  salla_status: string
  wordpress_functions?: string
  wordpress_integration: string
  wordpress_api_test: string
  wordpress_status: string
  future_app_ideas?: string
  future_app_planning?: string
  future_app_features?: string
  integration_issues?: string
  price_updates?: string
  created_at: string
  updated_at: string
}

export interface ProjectRecord {
  id: string
  user_id: string
  date: string
  period1_project?: string
  period1_tasks?: string
  period1_time: number
  period2_project?: string
  period2_tasks?: string
  period2_time: number
  dashsy_tasks?: string
  dashsy_progress: number
  dashsy_issues?: string
  dashsy_solutions?: string
  dashsy_tomorrow?: string
  syriana_tasks?: string
  syriana_progress: number
  syriana_issues?: string
  syriana_solutions?: string
  syriana_tomorrow?: string
  booking_tasks?: string
  booking_progress: number
  booking_issues?: string
  booking_solutions?: string
  booking_tomorrow?: string
  total_hours: number
  productivity_rating: number
  tomorrow_plan?: string
  created_at: string
  updated_at: string
}

export interface FinanceRecord {
  id: string
  user_id: string
  date: string
  shopify_revenue: number
  salla_revenue: number
  wordpress_revenue: number
  apps_revenue: number
  projects_revenue: number
  total_revenue: number
  daily_expenses: number
  net_profit: number
  monthly_cumulative: number
  financial_notes?: string
  created_at: string
  updated_at: string
}

export interface WeeklyRecord {
  id: string
  user_id: string
  week_start: string
  week_end: string
  routine_days: number
  worship_percentage: number
  total_learning_hours: number
  key_topics?: string
  laravel_progress: number
  mean_progress: number
  flutter_progress: number
  database_progress: number
  shopify_progress: number
  platform_progress: number
  apps_progress: number
  dashsy_progress: number
  syriana_progress: number
  booking_progress: number
  weekly_revenue: number
  main_challenges?: string
  main_solutions?: string
  next_week_plan?: string
  overall_rating: number
  created_at: string
  updated_at: string
}

export interface DashboardRecord {
  id: string
  user_id: string
  date: string
  routine_status: 'green' | 'yellow' | 'red'
  learning_progress: number
  templates_progress: number
  projects_progress: number
  daily_revenue: number
  monthly_routine_percentage: number
  monthly_learning_hours: number
  monthly_templates_progress: number
  monthly_projects_progress: number
  urgent_alerts?: string
  quick_notes?: string
  created_at: string
  updated_at: string
}
