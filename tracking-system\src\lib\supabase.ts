import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

// For client-side operations
export const supabase = createBrowserClient(supabaseUrl, supabaseAnonKey)

// For server-side operations
export const createServerClient = () => {
  return createClient(supabaseUrl, supabaseAnonKey)
}

// Database types
export interface User {
  id: string
  email: string
  full_name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface WorshipRecord {
  id: string
  user_id: string
  date: string
  sleep_on_time: boolean
  wake_up_on_time: boolean
  night_prayer: boolean
  fajr_prayer: boolean
  quran_reading: boolean
  istighfar_200: boolean
  salawat_200: boolean
  tasbih_100: boolean
  la_ilaha_illa_allah_100: boolean
  la_ilaha_illa_anta_50: boolean
  morning_exercise: boolean
  shower: boolean
  duha_prayer: boolean
  dhuhr_prayer: boolean
  asr_prayer: boolean
  maghrib_prayer: boolean
  isha_prayer: boolean
  slept_after_fajr: boolean
  worship_percentage: number
  spiritual_notes?: string
  created_at: string
  updated_at: string
}

export interface LearningRecord {
  id: string
  user_id: string
  date: string
  laravel_time: number
  laravel_topic?: string
  laravel_progress: number
  laravel_rating: number
  nodejs_time: number
  nodejs_topic?: string
  nodejs_progress: number
  nextjs_time: number
  nextjs_topic?: string
  nextjs_progress: number
  mean_rating: number
  flutter_time: number
  flutter_topic?: string
  flutter_progress: number
  database_time: number
  database_type?: string
  database_progress: number
  total_hours: number
  key_learnings?: string
  challenges?: string
  tomorrow_plan?: string
  created_at: string
  updated_at: string
}

export interface TemplateRecord {
  id: string
  user_id: string
  date: string
  shopify_time: number
  shopify_features?: string
  shopify_progress: number
  shopify_issues?: string
  shopify_solutions?: string
  salla_progress: number
  salla_notes?: string
  wordpress_progress: number
  wordpress_notes?: string
  platform_api?: string
  platform_functions?: string
  platform_testing: boolean
  wholesale_prices?: string
  expected_revenue: number
  tomorrow_plan?: string
  created_at: string
  updated_at: string
}

export interface AppRecord {
  id: string
  user_id: string
  date: string
  shopify_functions?: string
  shopify_integration: string
  shopify_api_test: string
  shopify_status: string
  salla_functions?: string
  salla_integration: string
  salla_api_test: string
  salla_status: string
  wordpress_functions?: string
  wordpress_integration: string
  wordpress_api_test: string
  wordpress_status: string
  future_app_ideas?: string
  future_app_planning?: string
  future_app_features?: string
  integration_issues?: string
  price_updates?: string
  created_at: string
  updated_at: string
}

export interface ProjectRecord {
  id: string
  user_id: string
  date: string
  period1_project?: string
  period1_tasks?: string
  period1_time: number
  period2_project?: string
  period2_tasks?: string
  period2_time: number
  dashsy_tasks?: string
  dashsy_progress: number
  dashsy_issues?: string
  dashsy_solutions?: string
  dashsy_tomorrow?: string
  syriana_tasks?: string
  syriana_progress: number
  syriana_issues?: string
  syriana_solutions?: string
  syriana_tomorrow?: string
  booking_tasks?: string
  booking_progress: number
  booking_issues?: string
  booking_solutions?: string
  booking_tomorrow?: string
  total_hours: number
  productivity_rating: number
  tomorrow_plan?: string
  created_at: string
  updated_at: string
}

export interface FinanceRecord {
  id: string
  user_id: string
  date: string
  shopify_revenue: number
  salla_revenue: number
  wordpress_revenue: number
  apps_revenue: number
  projects_revenue: number
  total_revenue: number
  daily_expenses: number
  net_profit: number
  monthly_cumulative: number
  financial_notes?: string
  created_at: string
  updated_at: string
}

export interface WeeklyRecord {
  id: string
  user_id: string
  week_start: string
  week_end: string
  routine_days: number
  worship_percentage: number
  total_learning_hours: number
  key_topics?: string
  laravel_progress: number
  mean_progress: number
  flutter_progress: number
  database_progress: number
  shopify_progress: number
  platform_progress: number
  apps_progress: number
  dashsy_progress: number
  syriana_progress: number
  booking_progress: number
  weekly_revenue: number
  main_challenges?: string
  main_solutions?: string
  next_week_plan?: string
  overall_rating: number
  created_at: string
  updated_at: string
}

export interface DashboardRecord {
  id: string
  user_id: string
  date: string
  routine_status: 'green' | 'yellow' | 'red'
  learning_progress: number
  templates_progress: number
  projects_progress: number
  daily_revenue: number
  monthly_routine_percentage: number
  monthly_learning_hours: number
  monthly_templates_progress: number
  monthly_projects_progress: number
  urgent_alerts?: string
  quick_notes?: string
  created_at: string
  updated_at: string
}
