import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const weekStart = searchParams.get('week_start')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    if (!userId) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 })
    }

    let query = supabase
      .from('weekly_records')
      .select('*')
      .eq('user_id', userId)

    if (weekStart) {
      query = query.eq('week_start', weekStart)
    } else if (startDate && endDate) {
      query = query.gte('week_start', startDate).lte('week_end', endDate)
    }

    const { data, error } = await query.order('week_start', { ascending: false })

    if (error) {
      console.error('Error fetching weekly records:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in GET /api/weekly:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      user_id,
      week_start,
      week_end,
      worship_average,
      learning_hours,
      project_hours,
      template_hours,
      app_hours,
      total_revenue,
      net_profit,
      goals_achieved,
      challenges_faced,
      lessons_learned,
      next_week_goals,
      overall_rating,
      weekly_notes
    } = body

    if (!user_id || !week_start || !week_end) {
      return NextResponse.json({ error: 'User ID, week start, and week end are required' }, { status: 400 })
    }

    const weeklyData = {
      user_id,
      week_start,
      week_end,
      worship_average: worship_average || 0,
      learning_hours: learning_hours || 0,
      project_hours: project_hours || 0,
      template_hours: template_hours || 0,
      app_hours: app_hours || 0,
      total_revenue: total_revenue || 0,
      net_profit: net_profit || 0,
      goals_achieved: goals_achieved || '',
      challenges_faced: challenges_faced || '',
      lessons_learned: lessons_learned || '',
      next_week_goals: next_week_goals || '',
      overall_rating: overall_rating || 5,
      weekly_notes: weekly_notes || ''
    }

    const { data, error } = await supabase
      .from('weekly_records')
      .insert(weeklyData)
      .select()
      .single()

    if (error) {
      console.error('Error creating weekly record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data, { status: 201 })
  } catch (error) {
    console.error('Error in POST /api/weekly:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      id,
      user_id,
      week_start,
      week_end,
      worship_average,
      learning_hours,
      project_hours,
      template_hours,
      app_hours,
      total_revenue,
      net_profit,
      goals_achieved,
      challenges_faced,
      lessons_learned,
      next_week_goals,
      overall_rating,
      weekly_notes
    } = body

    if (!id || !user_id) {
      return NextResponse.json({ error: 'ID and User ID are required' }, { status: 400 })
    }

    const weeklyData = {
      week_start,
      week_end,
      worship_average: worship_average || 0,
      learning_hours: learning_hours || 0,
      project_hours: project_hours || 0,
      template_hours: template_hours || 0,
      app_hours: app_hours || 0,
      total_revenue: total_revenue || 0,
      net_profit: net_profit || 0,
      goals_achieved: goals_achieved || '',
      challenges_faced: challenges_faced || '',
      lessons_learned: lessons_learned || '',
      next_week_goals: next_week_goals || '',
      overall_rating: overall_rating || 5,
      weekly_notes: weekly_notes || ''
    }

    const { data, error } = await supabase
      .from('weekly_records')
      .update(weeklyData)
      .eq('id', id)
      .eq('user_id', user_id)
      .select()
      .single()

    if (error) {
      console.error('Error updating weekly record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json(data)
  } catch (error) {
    console.error('Error in PUT /api/weekly:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get('id')
    const userId = searchParams.get('user_id')

    if (!id || !userId) {
      return NextResponse.json({ error: 'ID and User ID are required' }, { status: 400 })
    }

    const { error } = await supabase
      .from('weekly_records')
      .delete()
      .eq('id', id)
      .eq('user_id', userId)

    if (error) {
      console.error('Error deleting weekly record:', error)
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'Weekly record deleted successfully' })
  } catch (error) {
    console.error('Error in DELETE /api/weekly:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
