{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/lib/supabase.ts"], "sourcesContent": ["// Temporary mock implementation for Supabase\nconst mockSupabase = {\n  auth: {\n    getUser: () => Promise.resolve({ data: { user: null }, error: null }),\n    signOut: () => Promise.resolve({ error: null }),\n    signInWithPassword: () => Promise.resolve({ data: { user: null }, error: null }),\n    signUp: () => Promise.resolve({ data: { user: null }, error: null })\n  },\n  from: (table: string) => ({\n    select: () => ({\n      eq: () => ({\n        single: () => Promise.resolve({ data: null, error: null })\n      }),\n      order: () => Promise.resolve({ data: [], error: null })\n    }),\n    insert: () => Promise.resolve({ data: null, error: null }),\n    update: () => ({\n      eq: () => Promise.resolve({ data: null, error: null })\n    }),\n    delete: () => ({\n      eq: () => Promise.resolve({ data: null, error: null })\n    })\n  })\n}\n\n// For client-side operations\nexport const supabase = mockSupabase\n\n// For server-side operations\nexport const createServerClient = () => {\n  return mockSupabase\n}\n\n// Database types\nexport interface User {\n  id: string\n  email: string\n  full_name?: string\n  avatar_url?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WorshipRecord {\n  id: string\n  user_id: string\n  date: string\n  sleep_on_time: boolean\n  wake_up_on_time: boolean\n  night_prayer: boolean\n  fajr_prayer: boolean\n  quran_reading: boolean\n  istighfar_200: boolean\n  salawat_200: boolean\n  tasbih_100: boolean\n  la_ilaha_illa_allah_100: boolean\n  la_ilaha_illa_anta_50: boolean\n  morning_exercise: boolean\n  shower: boolean\n  duha_prayer: boolean\n  dhuhr_prayer: boolean\n  asr_prayer: boolean\n  maghrib_prayer: boolean\n  isha_prayer: boolean\n  slept_after_fajr: boolean\n  worship_percentage: number\n  spiritual_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface LearningRecord {\n  id: string\n  user_id: string\n  date: string\n  laravel_time: number\n  laravel_topic?: string\n  laravel_progress: number\n  laravel_rating: number\n  nodejs_time: number\n  nodejs_topic?: string\n  nodejs_progress: number\n  nextjs_time: number\n  nextjs_topic?: string\n  nextjs_progress: number\n  mean_rating: number\n  flutter_time: number\n  flutter_topic?: string\n  flutter_progress: number\n  database_time: number\n  database_type?: string\n  database_progress: number\n  total_hours: number\n  key_learnings?: string\n  challenges?: string\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface TemplateRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_time: number\n  shopify_features?: string\n  shopify_progress: number\n  shopify_issues?: string\n  shopify_solutions?: string\n  salla_progress: number\n  salla_notes?: string\n  wordpress_progress: number\n  wordpress_notes?: string\n  platform_api?: string\n  platform_functions?: string\n  platform_testing: boolean\n  wholesale_prices?: string\n  expected_revenue: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface AppRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_functions?: string\n  shopify_integration: string\n  shopify_api_test: string\n  shopify_status: string\n  salla_functions?: string\n  salla_integration: string\n  salla_api_test: string\n  salla_status: string\n  wordpress_functions?: string\n  wordpress_integration: string\n  wordpress_api_test: string\n  wordpress_status: string\n  future_app_ideas?: string\n  future_app_planning?: string\n  future_app_features?: string\n  integration_issues?: string\n  price_updates?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface ProjectRecord {\n  id: string\n  user_id: string\n  date: string\n  period1_project?: string\n  period1_tasks?: string\n  period1_time: number\n  period2_project?: string\n  period2_tasks?: string\n  period2_time: number\n  dashsy_tasks?: string\n  dashsy_progress: number\n  dashsy_issues?: string\n  dashsy_solutions?: string\n  dashsy_tomorrow?: string\n  syriana_tasks?: string\n  syriana_progress: number\n  syriana_issues?: string\n  syriana_solutions?: string\n  syriana_tomorrow?: string\n  booking_tasks?: string\n  booking_progress: number\n  booking_issues?: string\n  booking_solutions?: string\n  booking_tomorrow?: string\n  total_hours: number\n  productivity_rating: number\n  tomorrow_plan?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface FinanceRecord {\n  id: string\n  user_id: string\n  date: string\n  shopify_revenue: number\n  salla_revenue: number\n  wordpress_revenue: number\n  apps_revenue: number\n  projects_revenue: number\n  total_revenue: number\n  daily_expenses: number\n  net_profit: number\n  monthly_cumulative: number\n  financial_notes?: string\n  created_at: string\n  updated_at: string\n}\n\nexport interface WeeklyRecord {\n  id: string\n  user_id: string\n  week_start: string\n  week_end: string\n  routine_days: number\n  worship_percentage: number\n  total_learning_hours: number\n  key_topics?: string\n  laravel_progress: number\n  mean_progress: number\n  flutter_progress: number\n  database_progress: number\n  shopify_progress: number\n  platform_progress: number\n  apps_progress: number\n  dashsy_progress: number\n  syriana_progress: number\n  booking_progress: number\n  weekly_revenue: number\n  main_challenges?: string\n  main_solutions?: string\n  next_week_plan?: string\n  overall_rating: number\n  created_at: string\n  updated_at: string\n}\n\nexport interface DashboardRecord {\n  id: string\n  user_id: string\n  date: string\n  routine_status: 'green' | 'yellow' | 'red'\n  learning_progress: number\n  templates_progress: number\n  projects_progress: number\n  daily_revenue: number\n  monthly_routine_percentage: number\n  monthly_learning_hours: number\n  monthly_templates_progress: number\n  monthly_projects_progress: number\n  urgent_alerts?: string\n  quick_notes?: string\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;;AAC7C,MAAM,eAAe;IACnB,MAAM;QACJ,SAAS,IAAM,QAAQ,OAAO,CAAC;gBAAE,MAAM;oBAAE,MAAM;gBAAK;gBAAG,OAAO;YAAK;QACnE,SAAS,IAAM,QAAQ,OAAO,CAAC;gBAAE,OAAO;YAAK;QAC7C,oBAAoB,IAAM,QAAQ,OAAO,CAAC;gBAAE,MAAM;oBAAE,MAAM;gBAAK;gBAAG,OAAO;YAAK;QAC9E,QAAQ,IAAM,QAAQ,OAAO,CAAC;gBAAE,MAAM;oBAAE,MAAM;gBAAK;gBAAG,OAAO;YAAK;IACpE;IACA,MAAM,CAAC,QAAkB,CAAC;YACxB,QAAQ,IAAM,CAAC;oBACb,IAAI,IAAM,CAAC;4BACT,QAAQ,IAAM,QAAQ,OAAO,CAAC;oCAAE,MAAM;oCAAM,OAAO;gCAAK;wBAC1D,CAAC;oBACD,OAAO,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM,EAAE;4BAAE,OAAO;wBAAK;gBACvD,CAAC;YACD,QAAQ,IAAM,QAAQ,OAAO,CAAC;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACxD,QAAQ,IAAM,CAAC;oBACb,IAAI,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBACtD,CAAC;YACD,QAAQ,IAAM,CAAC;oBACb,IAAI,IAAM,QAAQ,OAAO,CAAC;4BAAE,MAAM;4BAAM,OAAO;wBAAK;gBACtD,CAAC;QACH,CAAC;AACH;AAGO,MAAM,WAAW;AAGjB,MAAM,qBAAqB;IAChC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 109, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder/tracking-system/src/app/signup/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { supabase } from '@/lib/supabase'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\nexport default function SignupPage() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [fullName, setFullName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [error, setError] = useState('')\n  const [success, setSuccess] = useState(false)\n  const router = useRouter()\n\n  const handleSignup = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setLoading(true)\n    setError('')\n\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n          data: {\n            full_name: fullName,\n          }\n        }\n      })\n\n      if (error) {\n        setError(error.message)\n      } else {\n        setSuccess(true)\n        setTimeout(() => {\n          router.push('/login')\n        }, 3000)\n      }\n    } catch (err) {\n      setError('حدث خطأ غير متوقع')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (success) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-emerald-100\" dir=\"rtl\">\n        <div className=\"max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg text-center\">\n          <div className=\"text-green-600\">\n            <svg className=\"mx-auto h-16 w-16\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">تم إنشاء الحساب بنجاح!</h2>\n          <p className=\"text-gray-600\">\n            تم إرسال رابط التفعيل إلى بريدك الإلكتروني. يرجى التحقق من البريد وتفعيل الحساب.\n          </p>\n          <p className=\"text-sm text-gray-500\">\n            سيتم توجيهك إلى صفحة تسجيل الدخول خلال 3 ثوانٍ...\n          </p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100\" dir=\"rtl\">\n      <div className=\"max-w-md w-full space-y-8 p-8 bg-white rounded-xl shadow-lg\">\n        <div className=\"text-center\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            🕌 نظام المتابعة الشامل\n          </h2>\n          <p className=\"text-gray-600\">إنشاء حساب جديد</p>\n        </div>\n\n        <form className=\"space-y-6\" onSubmit={handleSignup}>\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg\">\n              {error}\n            </div>\n          )}\n\n          <div>\n            <label htmlFor=\"fullName\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              الاسم الكامل\n            </label>\n            <input\n              id=\"fullName\"\n              type=\"text\"\n              required\n              value={fullName}\n              onChange={(e) => setFullName(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"أدخل اسمك الكامل\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              البريد الإلكتروني\n            </label>\n            <input\n              id=\"email\"\n              type=\"email\"\n              required\n              value={email}\n              onChange={(e) => setEmail(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"أدخل بريدك الإلكتروني\"\n            />\n          </div>\n\n          <div>\n            <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 mb-2\">\n              كلمة المرور\n            </label>\n            <input\n              id=\"password\"\n              type=\"password\"\n              required\n              minLength={6}\n              value={password}\n              onChange={(e) => setPassword(e.target.value)}\n              className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n              placeholder=\"أدخل كلمة المرور (6 أحرف على الأقل)\"\n            />\n          </div>\n\n          <button\n            type=\"submit\"\n            disabled={loading}\n            className=\"w-full flex justify-center py-2 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {loading ? 'جاري إنشاء الحساب...' : 'إنشاء حساب'}\n          </button>\n\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600\">\n              لديك حساب بالفعل؟{' '}\n              <Link href=\"/login\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n                تسجيل الدخول\n              </Link>\n            </p>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjD;gBACA;gBACA,SAAS;oBACP,MAAM;wBACJ,WAAW;oBACb;gBACF;YACF;YAEA,IAAI,OAAO;gBACT,SAAS,MAAM,OAAO;YACxB,OAAO;gBACL,WAAW;gBACX,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;YAA+F,KAAI;sBAChH,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAoB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC3E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAG7B,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBACE,8OAAC;QAAI,WAAU;QAA6F,KAAI;kBAC9G,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAG/B,8OAAC;oBAAK,WAAU;oBAAY,UAAU;;wBACnC,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;8CAA+C;;;;;;8CAGhF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;8CAA+C;;;;;;8CAGnF,8OAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAQ;oCACR,WAAW;oCACX,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,8OAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,UAAU,yBAAyB;;;;;;sCAGtC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;oCACjB;kDAClB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS5F", "debugId": null}}]}